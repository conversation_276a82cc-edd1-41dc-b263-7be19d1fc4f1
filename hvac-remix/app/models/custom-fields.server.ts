import { prisma } from "~/db.server";
import type { CustomFieldValue } from "@prisma/client";
import { prisma } from "~/db.server";
import type { EntityType, ServiceResponse } from "~/models/metadata.server";

/**
 * Get custom field values for a specific entity
 */
export async function getEntityCustomFields(
  entityId: string,
  entityType: EntityType
): Promise<Record<string, any>> {
  // Get all custom fields for this entity type
  const fields = await prisma.customField.findMany({
    where: {
      entityType,
      isActive: true
    }
  });

  // Get all values for this entity
  const values = await prisma.customFieldValue.findMany({
    where: {
      entityId,
      field: {
        entityType
      }
    },
    include: {
      field: true
    }
  });

  // Create a map of field name to value
  const result: Record<string, any> = {};

  // First set default values for all fields
  fields.forEach(field => {
    result[field.name] = field.defaultValue || null;
  });

  // Then override with actual values
  values.forEach(value => {
    // Convert value based on field type
    let parsedValue: any = value.value;

    switch (value.field.fieldType) {
      case 'NUMBER':
        parsedValue = parseFloat(value.value);
        break;
      case 'BOOLEAN':
        parsedValue = value.value === 'true';
        break;
      case 'DATE':
        parsedValue = new Date(value.value);
        break;
      case 'MULTI_SELECT':
        parsedValue = JSON.parse(value.value);
        break;
    }

    result[value.field.name] = parsedValue;
  });

  return result;
}

/**
 * Save custom field values for a specific entity
 */
export async function saveEntityCustomFields(
  entityId: string,
  entityType: EntityType,
  values: Record<string, any>
): Promise<ServiceResponse<boolean>> {
  try {
    // Get all custom fields for this entity type
    const fields = await prisma.customField.findMany({
      where: {
        entityType,
        isActive: true
      }
    });

    // Create a map of field name to field
    const fieldMap = new Map(fields.map(field => [field.name, field]));

    // Process each value
    for (const [fieldName, value] of Object.entries(values)) {
      const field = fieldMap.get(fieldName);

      // Skip if field doesn't exist
      if (!field) continue;

      // Convert value to string based on field type
      let stringValue: string;

      switch (field.fieldType) {
        case 'BOOLEAN':
          stringValue = value ? 'true' : 'false';
          break;
        case 'MULTI_SELECT':
          stringValue = JSON.stringify(value);
          break;
        case 'DATE':
          stringValue = value instanceof Date
            ? value.toISOString()
            : new Date(value).toISOString();
          break;
        default:
          stringValue = String(value);
      }

      // Upsert the value
      await prisma.customFieldValue.upsert({
        where: {
          fieldId_entityId: {
            fieldId: field.id,
            entityId
          }
        },
        update: {
          value: stringValue
        },
        create: {
          fieldId: field.id,
          entityId,
          value: stringValue
        }
      });
    }

    return {
      success: true,
      data: true,
      error: null
    };
  } catch (error) {
    console.error("Error saving custom field values:", error);
    return {
      success: false,
      data: null,
      error: "Failed to save custom field values"
    };
  }
}

/**
 * Get entities with custom field values
 */
export async function getEntitiesWithCustomFields<T>(
  entityType: EntityType,
  entities: T[],
  idField: keyof T = 'id' as keyof T
): Promise<(T & { customFields: Record<string, any> })[]> {
  // Get all entity IDs
  const entityIds = entities.map(entity => String(entity[idField]));

  // Get all custom fields for this entity type
  const fields = await prisma.customField.findMany({
    where: {
      entityType,
      isActive: true
    }
  });

  // Get all values for these entities
  const values = await prisma.customFieldValue.findMany({
    where: {
      entityId: { in: entityIds },
      field: {
        entityType
      }
    },
    include: {
      field: true
    }
  });

  // Group values by entity ID
  const valuesByEntityId = values.reduce((acc, value) => {
    if (!acc[value.entityId]) {
      acc[value.entityId] = [];
    }
    acc[value.entityId].push(value);
    return acc;
  }, {} as Record<string, CustomFieldValue[]>);

  // Add custom fields to each entity
  return entities.map(entity => {
    const entityId = String(entity[idField]);
    const entityValues = valuesByEntityId[entityId] || [];

    // Create a map of field name to value
    const customFields: Record<string, any> = {};

    // First set default values for all fields
    fields.forEach(field => {
      customFields[field.name] = field.defaultValue || null;
    });

    // Then override with actual values
    entityValues.forEach(value => {
      // Convert value based on field type
      let parsedValue: any = value.value;

      switch (value.field.fieldType) {
        case 'NUMBER':
          parsedValue = parseFloat(value.value);
          break;
        case 'BOOLEAN':
          parsedValue = value.value === 'true';
          break;
        case 'DATE':
          parsedValue = new Date(value.value);
          break;
        case 'MULTI_SELECT':
          parsedValue = JSON.parse(value.value);
          break;
      }

      customFields[value.field.name] = parsedValue;
    });

    return {
      ...entity,
      customFields
    };
  });
}