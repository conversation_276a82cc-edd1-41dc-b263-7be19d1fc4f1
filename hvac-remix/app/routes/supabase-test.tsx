import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { supabase } from "~/supabase.server";
import { prisma } from "~/db.server";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Test Supabase connection
    const { data: supabaseTables, error: supabaseTablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(20);

    // Test Prisma connection
    let prismaSuccess = false;
    let prismaData = null;
    let prismaError = null;

    try {
      prismaData = await prisma.user.findMany({ take: 5 });
      prismaSuccess = true;
    } catch (err) {
      prismaError = err instanceof Error ? err.message : "Unknown Prisma error";
      console.error("Prisma connection error:", err);
    }

    return json({
      supabase: {
        success: !supabaseTablesError,
        tables: supabaseTables,
        error: supabaseTablesError ? supabaseTablesError.message : null,
      },
      prisma: {
        success: prismaSuccess,
        data: prismaData,
        error: prismaError,
      },
    });
  } catch (error) {
    console.error("Error testing database connections:", error);
    return json({
      supabase: {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      prisma: {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
}

export default function SupabaseTest() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Database Connection Test</h1>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Supabase Connection</h2>
        <div className="p-4 border rounded">
          <p className="mb-2">
            Status: <span className={data.supabase.success ? "text-green-600" : "text-red-600"}>
              {data.supabase.success ? "Connected" : "Failed"}
            </span>
          </p>
          {data.supabase.error && (
            <p className="text-red-600 mb-2">Error: {data.supabase.error}</p>
          )}
          {data.supabase.tables && (
            <div>
              <p className="mb-2">Tables in Supabase:</p>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-60">
                {JSON.stringify(data.supabase.tables, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-2">Prisma Connection</h2>
        <div className="p-4 border rounded">
          <p className="mb-2">
            Status: <span className={data.prisma.success ? "text-green-600" : "text-red-600"}>
              {data.prisma.success ? "Connected" : "Failed"}
            </span>
          </p>
          {data.prisma.error && (
            <p className="text-red-600 mb-2">Error: {data.prisma.error}</p>
          )}
          {data.prisma.data && (
            <div>
              <p className="mb-2">Data:</p>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-60">
                {JSON.stringify(data.prisma.data, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
