/**
 * Agent Dashboard API Route
 * Provides dashboard data for agent monitoring and management
 */

import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { AgentProtocolClient } from '~/lib/agent-protocol-client/client';
export async function loader({ }: LoaderFunctionArgs) {
  try {
    // Initialize Agent Protocol client
    const client = new AgentProtocolClient({
      baseUrl: process.env.AGENT_PROTOCOL_URL || 'http://localhost:8001',
      apiKey: process.env.AGENT_PROTOCOL_API_KEY,
    });

    // Get all agents
    const agentsResponse = await client.listAgents();
    
    if (!agentsResponse.success) {
      return json(
        {
          success: false,
          error: 'Failed to fetch agents',
          details: agentsResponse.error,
        },
        { status: 500 }
      );
    }

    const agents = agentsResponse.data?.items || [];
    
    // Get detailed status for each agent
    const agentStatuses = await Promise.all(
      agents.map(async (agent) => {
        try {
          // Get tasks for this agent
          const tasksResponse = await client.listTasks(agent.agent_id);
          const tasks = tasksResponse.success ? tasksResponse.data?.items || [] : [];
          
          // Calculate metrics
          const activeTasks = tasks.filter(task => 
            task.status === 'running' || task.status === 'created'
          ).length;
          
          const completedTasks = tasks.filter(task => 
            task.status === 'completed'
          ).length;
          


          // Calculate average response time for completed tasks
          const completedTasksWithTimes = tasks.filter(task => 
            task.status === 'completed' && task.created_at && task.updated_at
          );
          
          const averageResponseTime = completedTasksWithTimes.length > 0
            ? completedTasksWithTimes.reduce((sum, task) => {
                const duration = new Date(task.updated_at).getTime() - new Date(task.created_at).getTime();
                return sum + (duration / 1000); // Convert to seconds
              }, 0) / completedTasksWithTimes.length
            : 0;

          // Determine agent status
          let status: 'online' | 'offline' | 'busy' = 'offline';
          if (activeTasks > 0) {
            status = 'busy';
          } else if (tasks.length > 0) {
            // Check if there's been recent activity (within last hour)
            const lastActivity = tasks.reduce((latest, task) => {
              const taskTime = new Date(task.updated_at);
              return taskTime > latest ? taskTime : latest;
            }, new Date(0));
            
            const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
            status = lastActivity > oneHourAgo ? 'online' : 'offline';
          }

          const lastActivity = tasks.length > 0
            ? tasks.reduce((latest, task) => {
                const taskTime = new Date(task.updated_at);
                return taskTime > latest ? taskTime : latest;
              }, new Date(0))
            : new Date(agent.updated_at);

          return {
            agent,
            activeTasks,
            activeThreads: 0, // We'd need to implement thread counting
            completedTasks,
            averageResponseTime: Math.round(averageResponseTime),
            status,
            lastActivity,
          };
        } catch (error) {
          console.error(`Error getting status for agent ${agent.agent_id}:`, error);
          return {
            agent,
            activeTasks: 0,
            activeThreads: 0,
            completedTasks: 0,
            averageResponseTime: 0,
            status: 'offline' as const,
            lastActivity: new Date(agent.updated_at),
          };
        }
      })
    );

    // Calculate dashboard stats
    const totalAgents = agents.length;
    const activeAgents = agentStatuses.filter(status => status.status !== 'offline').length;
    const totalTasks = agentStatuses.reduce((sum, status) => 
      sum + status.activeTasks + status.completedTasks, 0
    );
    const completedTasks = agentStatuses.reduce((sum, status) => 
      sum + status.completedTasks, 0
    );
    const averageTaskTime = agentStatuses.length > 0
      ? Math.round(
          agentStatuses.reduce((sum, status) => sum + status.averageResponseTime, 0) / 
          agentStatuses.length
        )
      : 0;
    const successRate = totalTasks > 0 
      ? Math.round((completedTasks / totalTasks) * 100)
      : 100;

    const dashboardStats = {
      totalAgents,
      activeAgents,
      totalTasks,
      completedTasks,
      averageTaskTime,
      successRate,
    };

    // Get recent tasks across all agents
    const allTasks = await Promise.all(
      agents.map(async (agent) => {
        const tasksResponse = await client.listTasks(agent.agent_id);
        return tasksResponse.success ? tasksResponse.data?.items || [] : [];
      })
    );

    const recentTasks = allTasks
      .flat()
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10); // Get 10 most recent tasks

    return json({
      success: true,
      agentStatuses,
      dashboardStats,
      recentTasks,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error loading dashboard data:', error);
    return json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
