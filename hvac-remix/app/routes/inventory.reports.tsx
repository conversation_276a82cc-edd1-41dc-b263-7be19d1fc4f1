import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { ArchiveBoxIcon, DocumentChartBarIcon, ArrowDownTrayIcon } from "@heroicons/react/24/outline";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Get query parameters
  const url = new URL(request.url);
  const reportType = url.searchParams.get("type") || "value";
  const period = url.searchParams.get("period") || "month";
  
  // Get current date and start date based on period
  const endDate = new Date();
  let startDate = new Date();
  
  switch (period) {
    case "week":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "month":
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case "quarter":
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case "year":
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    default:
      startDate.setMonth(endDate.getMonth() - 1);
  }
  
  // Get inventory transactions for the period
  const transactions = await prisma.inventoryTransaction.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    },
    include: {
      part: true
    },
    orderBy: {
      createdAt: "desc"
    }
  });
  
  // Get inventory parts
  const parts = await prisma.inventoryPart.findMany({
    where: {
      isActive: true
    },
    include: {
      supplier: true,
      inventoryLocations: {
        include: {
          location: true
        }
      }
    }
  });
  
  // Calculate inventory value by category
  const valueByCategory = parts.reduce((acc, part) => {
    const category = part.category || "Uncategorized";
    if (!acc[category]) {
      acc[category] = {
        totalValue: 0,
        count: 0,
        items: []
      };
    }
    
    acc[category].totalValue += (part.costPrice || 0) * (part.currentStock || 0);
    acc[category].count += 1;
    acc[category].items.push(part);
    
    return acc;
  }, {} as Record<string, { totalValue: number, count: number, items: any[] }>);
  
  // Calculate inventory value by location
  const valueByLocation: Record<string, { totalValue: number, count: number, items: any[] }> = {};
  
  parts.forEach(part => {
    part.inventoryLocations.forEach(locItem => {
      const locationId = locItem.locationId;
      const locationName = locItem.location.name;
      const key = `${locationId}:${locationName}`;
      
      if (!valueByLocation[key]) {
        valueByLocation[key] = {
          totalValue: 0,
          count: 0,
          items: []
        };
      }
      
      const itemValue = locItem.quantity * (part.costPrice || 0);
      valueByLocation[key].totalValue += itemValue;
      valueByLocation[key].count += 1;
      valueByLocation[key].items.push({
        ...part,
        quantityAtLocation: locItem.quantity,
        valueAtLocation: itemValue
      });
    });
  });
  
  // Calculate usage statistics
  const usageTransactions = transactions.filter(t => t.type === "OUTBOUND");
  
  // Group usage by part
  const usageByPart = usageTransactions.reduce((acc, transaction) => {
    const partId = transaction.partId;
    if (!acc[partId]) {
      acc[partId] = {
        part: transaction.part,
        totalQuantity: 0,
        totalValue: 0,
        transactions: []
      };
    }
    
    acc[partId].totalQuantity += Math.abs(transaction.quantity);
    acc[partId].totalValue += Math.abs(transaction.quantity) * (transaction.part.costPrice || 0);
    acc[partId].transactions.push(transaction);
    
    return acc;
  }, {} as Record<string, { part: any, totalQuantity: number, totalValue: number, transactions: any[] }>);
  
  // Calculate movement statistics (receipts, usage, transfers, adjustments)
  const movementStats = {
    receipts: transactions.filter(t => t.transactionType === "RECEIPT").length,
    usage: transactions.filter(t => t.transactionType === "USAGE").length,
    transfers: transactions.filter(t => t.transactionType === "TRANSFER").length,
    adjustments: transactions.filter(t => t.transactionType === "ADJUSTMENT").length,
    total: transactions.length
  };
  
  // Calculate total value used
  const totalValueUsed = Object.values(usageByPart).reduce((sum, item) => sum + item.totalValue, 0);
  
  // Calculate current inventory value
  const totalInventoryValue = parts.reduce((sum, part) => sum + ((part.costPrice || 0) * (part.currentStock || 0)), 0);
  
  return json({
    reportType,
    period,
    transactions,
    usageByPart: Object.values(usageByPart).sort((a, b) => b.totalValue - a.totalValue),
    valueByCategory,
    valueByLocation,
    movementStats,
    totalValueUsed,
    totalInventoryValue,
    startDate,
    endDate
  });
}

export default function InventoryReports() {
  const { 
    reportType, 
    period, 
    transactions, 
    usageByPart, 
    valueByCategory,
    valueByLocation,
    movementStats, 
    totalValueUsed, 
    totalInventoryValue,
    startDate,
    endDate
  } = useLoaderData<typeof loader>();
  
  // Format dates for display
  const formattedStartDate = new Date(startDate).toLocaleDateString();
  const formattedEndDate = new Date(endDate).toLocaleDateString();
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Inventory Reports</h1>
        <div className="flex gap-2">
          <Link 
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Inventory List
          </Link>
          <button 
            className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <ArrowDownTrayIcon className="h-4 w-4" />
            Export Report
          </button>
        </div>
      </div>
      
      {/* Report Filters */}
      <div className="rounded-md border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 p-4">
          <form className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div>
              <label htmlFor="reportType" className="block text-sm font-medium text-gray-700">Report Type</label>
              <select 
                id="reportType" 
                name="type" 
                defaultValue={reportType}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="value">Value Report</option>
                <option value="usage">Usage Report</option>
                <option value="movement">Movement Report</option>
                <option value="location">Location Report</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="period" className="block text-sm font-medium text-gray-700">Time Period</label>
              <select 
                id="period" 
                name="period" 
                defaultValue={period}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="quarter">Last 90 Days</option>
                <option value="year">Last 365 Days</option>
              </select>
            </div>
            
            <div className="mt-6">
              <button 
                type="submit" 
                className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
              >
                Generate Report
              </button>
            </div>
          </form>
        </div>
      </div>
      
      {/* Report Summary */}
      <div className="rounded-md border border-gray-200 bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">
          {reportType === 'value' && 'Inventory Value Report'}
          {reportType === 'usage' && 'Inventory Usage Report'}
          {reportType === 'movement' && 'Inventory Movement Report'}
          {reportType === 'location' && 'Inventory Location Report'}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          Report period: {formattedStartDate} to {formattedEndDate}
        </p>
        
        <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentChartBarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Total Inventory Value</dt>
                    <dd className="text-xl font-semibold text-gray-900">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalInventoryValue)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          
          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentChartBarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Value Used (Period)</dt>
                    <dd className="text-xl font-semibold text-gray-900">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalValueUsed)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          
          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentChartBarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Total Transactions</dt>
                    <dd className="text-xl font-semibold text-gray-900">
                      {movementStats.total}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          
          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentChartBarIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Usage Transactions</dt>
                    <dd className="text-xl font-semibold text-gray-900">
                      {movementStats.usage}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Report Content - Conditional based on report type */}
      {reportType === 'value' && (
        <ValueReport valueByCategory={valueByCategory} totalInventoryValue={totalInventoryValue} />
      )}
      
      {reportType === 'usage' && (
        <UsageReport usageByPart={usageByPart} totalValueUsed={totalValueUsed} />
      )}
      
      {reportType === 'movement' && (
        <MovementReport transactions={transactions} movementStats={movementStats} />
      )}
      
      {reportType === 'location' && (
        <LocationReport valueByLocation={valueByLocation} totalInventoryValue={totalInventoryValue} />
      )}
    </div>
  );
}

function ValueReport({ valueByCategory, totalInventoryValue }: { valueByCategory: any, totalInventoryValue: number }) {
  // Convert to array for sorting
  const categoriesArray = Object.entries(valueByCategory).map(([category, data]: [string, any]) => ({
    category,
    ...data,
    percentageOfTotal: (data.totalValue / totalInventoryValue) * 100
  })).sort((a, b) => b.totalValue - a.totalValue);
  
  return (
    <div className="space-y-6">
      {/* Visualizations would go here - placeholder for now */}
      <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Value distribution visualization would go here</p>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory Value by Category</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Value
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    % of Total
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Number of Parts
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Avg. Value per Part
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {categoriesArray.map((category) => (
                  <tr key={category.category}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {category.category}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(category.totalValue)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {category.percentageOfTotal.toFixed(2)}%
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {category.count}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(category.totalValue / category.count)}
                    </td>
                  </tr>
                ))}
                <tr className="bg-gray-50 font-semibold">
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    Total
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalInventoryValue)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    100.00%
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {categoriesArray.reduce((sum, cat) => sum + cat.count, 0)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(
                      totalInventoryValue / categoriesArray.reduce((sum, cat) => sum + cat.count, 0)
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Highest Value Parts</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Stock
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Cost Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Value
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    % of Inventory
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {categoriesArray.flatMap(category => 
                  category.items
                    .sort((a: any, b: any) => (b.currentValuePLN || 0) - (a.currentValuePLN || 0))
                    .slice(0, 2) // Get top 2 items from each category
                ).sort((a: any, b: any) => (b.currentValuePLN || 0) - (a.currentValuePLN || 0))
                  .slice(0, 10) // Get overall top 10
                  .map((part: any) => (
                    <tr key={part.id}>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                        <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                          {part.name}
                        </Link>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {part.category || 'Uncategorized'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {part.currentStock} {part.unitOfMeasure}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.costPrice || 0)}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.currentValuePLN || 0)}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {(((part.currentValuePLN || 0) / totalInventoryValue) * 100).toFixed(2)}%
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function UsageReport({ usageByPart, totalValueUsed }: { usageByPart: any[], totalValueUsed: number }) {
  return (
    <div className="space-y-6">
      {/* Visualizations would go here - placeholder for now */}
      <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Usage trends visualization would go here</p>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Most Used Parts by Value</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity Used
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Cost
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    % of Usage
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Stock
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {usageByPart.slice(0, 15).map((item) => (
                  <tr key={item.part.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${item.part.id}`} className="text-blue-600 hover:underline">
                        {item.part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {item.part.category || 'Uncategorized'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {item.totalQuantity} {item.part.unitOfMeasure}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(item.totalValue)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {((item.totalValue / totalValueUsed) * 100).toFixed(2)}%
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {item.part.currentStock} {item.part.unitOfMeasure}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Usage by Category</h3>
            <div className="mt-5 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Total Value Used
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      % of Usage
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {(() => {
                    // Calculate usage by category
                    const usageByCategory = usageByPart.reduce((acc, item) => {
                      const category = item.part.category || 'Uncategorized';
                      if (!acc[category]) {
                        acc[category] = {
                          totalValue: 0,
                          count: 0
                        };
                      }
                      
                      acc[category].totalValue += item.totalValue;
                      acc[category].count += 1;
                      
                      return acc;
                    }, {} as Record<string, { totalValue: number, count: number }>);
                    
                    return Object.entries(usageByCategory)
                      .sort(([,a]: [string, any], [,b]: [string, any]) => b.totalValue - a.totalValue)
                      .map(([category, data]: [string, any]) => (
                        <tr key={category}>
                          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                            {category}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(data.totalValue)}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {((data.totalValue / totalValueUsed) * 100).toFixed(2)}%
                          </td>
                        </tr>
                      ));
                  })()} 
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Usage Patterns</h3>
            <div className="mt-5 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Week Day
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Transactions
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Value Used
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {(() => {
                    // Group by day of week
                    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                    const usageByDay = usageByPart.flatMap(item => item.transactions)
                      .reduce((acc, transaction) => {
                        const date = new Date(transaction.transactionDate);
                        const day = date.getDay();
                        const dayName = dayNames[day];
                        
                        if (!acc[day]) {
                          acc[day] = {
                            dayName,
                            count: 0,
                            value: 0
                          };
                        }
                        
                        acc[day].count += 1;
                        acc[day].value += Math.abs(transaction.quantity) * (transaction.part.costPrice || 0);
                        
                        return acc;
                      }, {} as Record<number, { dayName: string, count: number, value: number }>);
                    
                    return Object.values(usageByDay)
                      .sort((a, b) => dayNames.indexOf(a.dayName) - dayNames.indexOf(b.dayName))
                      .map(day => (
                        <tr key={day.dayName}>
                          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                            {day.dayName}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {day.count}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(day.value)}
                          </td>
                        </tr>
                      ));
                  })()}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function MovementReport({ transactions, movementStats }: { transactions: any[], movementStats: any }) {
  return (
    <div className="space-y-6">
      {/* Visualizations would go here - placeholder for now */}
      <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Movement trends visualization would go here</p>
      </div>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Transaction Type Summary</h3>
            <div className="mt-5">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Transaction Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      Count
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                      % of Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">Receipts</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{movementStats.receipts}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {((movementStats.receipts / movementStats.total) * 100).toFixed(2)}%
                    </td>
                  </tr>
                  <tr>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">Usage</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{movementStats.usage}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {((movementStats.usage / movementStats.total) * 100).toFixed(2)}%
                    </td>
                  </tr>
                  <tr>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">Transfers</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{movementStats.transfers}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {((movementStats.transfers / movementStats.total) * 100).toFixed(2)}%
                    </td>
                  </tr>
                  <tr>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">Adjustments</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{movementStats.adjustments}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {((movementStats.adjustments / movementStats.total) * 100).toFixed(2)}%
                    </td>
                  </tr>
                  <tr className="bg-gray-50 font-semibold">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">Total</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">{movementStats.total}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">100.00%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Daily Transaction Trend</h3>
            <div className="mt-5">
              {/* Placeholder for a graph */}
              <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
                <p className="text-gray-500">Daily transaction volume graph would go here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Recent Transactions</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reference
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {transactions.slice(0, 20).map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Date(transaction.transactionDate).toLocaleDateString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        transaction.transactionType === 'RECEIPT' ? 'bg-green-100 text-green-800' :
                        transaction.transactionType === 'USAGE' ? 'bg-red-100 text-red-800' :
                        transaction.transactionType === 'TRANSFER' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {transaction.transactionType}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${transaction.partId}`} className="text-blue-600 hover:underline">
                        {transaction.part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {Math.abs(transaction.quantity)} {transaction.part.unitOfMeasure}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.location?.name || 'N/A'}
                      {transaction.transferToLocationId && (
                        <> → {transaction.transferToLocation?.name || 'Unknown'}</>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.user?.name || 'System'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.referenceType && transaction.referenceId ? (
                        <Link 
                          to={`/${transaction.referenceType.toLowerCase()}s/${transaction.referenceId}`}
                          className="text-blue-600 hover:underline"
                        >
                          {transaction.referenceType.replace('_', ' ')} #{transaction.referenceId.substring(0, 8)}
                        </Link>
                      ) : 
                      transaction.reason || 'N/A'}
                    </td>
                  </tr>
                ))}
                {transactions.length === 0 && (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      No transactions found for the selected period.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function LocationReport({ valueByLocation, totalInventoryValue }: { valueByLocation: any, totalInventoryValue: number }) {
  // Convert to array for sorting
  const locationsArray = Object.entries(valueByLocation).map(([key, data]: [string, any]) => {
    const [locationId, locationName] = key.split(':');
    return {
      locationId,
      locationName,
      ...data,
      percentageOfTotal: (data.totalValue / totalInventoryValue) * 100
    };
  }).sort((a, b) => b.totalValue - a.totalValue);
  
  return (
    <div className="space-y-6">
      {/* Visualizations would go here - placeholder for now */}
      <div className="h-64 rounded-lg border border-gray-200 bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Location distribution visualization would go here</p>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory Value by Location</h3>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total Value
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    % of Total
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Unique Parts
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {locationsArray.map((location) => (
                  <tr key={location.locationId}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {location.locationName}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(location.totalValue)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {location.percentageOfTotal.toFixed(2)}%
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {location.count}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <Link 
                        to={`/inventory?location=${location.locationId}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Parts
                      </Link>
                    </td>
                  </tr>
                ))}
                <tr className="bg-gray-50 font-semibold">
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    Total
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalInventoryValue)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    100.00%
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {/* This is distinct count, not a sum of counts */}
                    -
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Top Parts by Location</h3>
          <div className="mt-5">
            {/* Accordion or tabs for locations would be nice here, but using simple sections for now */}
            {locationsArray.slice(0, 3).map((location) => (
              <div key={location.locationId} className="mb-6">
                <h4 className="mb-3 text-base font-medium text-gray-900">{location.locationName}</h4>
                <div className="overflow-x-auto rounded-md border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Part Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Quantity
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Value
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          % of Location
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {location.items
                        .sort((a: any, b: any) => b.valueAtLocation - a.valueAtLocation)
                        .slice(0, 5)
                        .map((item: any) => (
                          <tr key={item.id}>
                            <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                              <Link to={`/inventory/parts/${item.id}`} className="text-blue-600 hover:underline">
                                {item.name}
                              </Link>
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {item.quantityAtLocation} {item.unitOfMeasure}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(item.valueAtLocation)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                              {((item.valueAtLocation / location.totalValue) * 100).toFixed(2)}%
                            </td>
                          </tr>
                        ))}
                      {location.items.length === 0 && (
                        <tr>
                          <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                            No parts in this location.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}