import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";
import { RichTextEditor, plateValueToString, stringToPlateValue } from "~/components/ui/rich-text-editor";
import { useState } from "react";
import type { Value } from "@udecode/plate";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  const serviceOrders = await prisma.serviceOrder.findMany({
    where: {
      userId,
      status: { not: "CANCELLED" },
    },
    include: {
      customer: true,
      device: true,
    },
    orderBy: { createdAt: "desc" },
  });

  return json({ serviceOrders });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);

  const formData = await request.formData();
  const serviceOrderId = formData.get("serviceOrderId") as string;
  const title = formData.get("title") as string;

  // Get the rich text editor values as JSON strings
  const descriptionJson = formData.get("description") as string || undefined;
  const workPerformedJson = formData.get("workPerformed") as string || undefined;
  const partsUsed = formData.get("partsUsed") as string || undefined;
  const recommendationsJson = formData.get("recommendations") as string || undefined;

  // Parse the JSON strings and convert to plain text for storage
  // This allows us to store the formatted content as JSON but also have plain text for searching
  let description: string | undefined;
  let workPerformed: string | undefined;
  let recommendations: string | undefined;

  try {
    if (descriptionJson) {
      const descriptionValue = JSON.parse(descriptionJson) as Value;
      description = plateValueToString(descriptionValue);
    }

    if (workPerformedJson) {
      const workPerformedValue = JSON.parse(workPerformedJson) as Value;
      workPerformed = plateValueToString(workPerformedValue);
    }

    if (recommendationsJson) {
      const recommendationsValue = JSON.parse(recommendationsJson) as Value;
      recommendations = plateValueToString(recommendationsValue);
    }
  } catch (e) {
    console.error("Error parsing rich text content:", e);
    // If parsing fails, use the raw values
    description = descriptionJson;
    workPerformed = workPerformedJson;
    recommendations = recommendationsJson;
  }

  if (!serviceOrderId) {
    return json({ error: "Service Order is required" }, { status: 400 });
  }

  if (!title) {
    return json({ error: "Title is required" }, { status: 400 });
  }

  try {
    // Verify service order belongs to user
    const serviceOrder = await prisma.serviceOrder.findUnique({
      where: { id: serviceOrderId },
    });

    if (!serviceOrder || serviceOrder.userId !== userId) {
      return json({ error: "Service Order not found or access denied" }, { status: 403 });
    }

    // Store both the plain text and the JSON representation
    const serviceReport = await prisma.serviceReport.create({
      data: {
        title,
        description,
        workPerformed,
        partsUsed,
        recommendations,
        // Store the rich text content in metadata
        metadata: {
          descriptionRichText: descriptionJson,
          workPerformedRichText: workPerformedJson,
          recommendationsRichText: recommendationsJson,
        },
        ocrProcessingStatus: "PENDING",
        serviceOrder: { connect: { id: serviceOrderId } },
      },
    });

    return redirect(`/service-reports/${serviceReport.id}`);
  } catch (error) {
    console.error("Error creating service report:", error);
    return json({ error: "An error occurred while creating the service report" }, { status: 500 });
  }
}

export default function NewServiceReport() {
  const { serviceOrders } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  // State for rich text editors
  const [descriptionValue, setDescriptionValue] = useState<Value>([
    { type: 'p', children: [{ text: '' }] },
  ]);
  const [workPerformedValue, setWorkPerformedValue] = useState<Value>([
    { type: 'p', children: [{ text: '' }] },
  ]);
  const [recommendationsValue, setRecommendationsValue] = useState<Value>([
    { type: 'p', children: [{ text: '' }] },
  ]);

  const isSubmitting = navigation.state === "submitting";

  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link to="/service-reports" className="mr-4 text-gray-500 hover:text-gray-700">
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <h2 className="text-xl font-semibold">New Service Report</h2>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Service Report Information</h3>
        </div>
        <div className="px-6 py-4">
          <Form method="post">
            {actionData?.error && (
              <div className="mb-4 rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{actionData.error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="serviceOrderId" className="block text-sm font-medium text-gray-700">
                Service Order <span className="text-red-500">*</span>
              </label>
              <select
                id="serviceOrderId"
                name="serviceOrderId"
                required
                className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">Select a service order</option>
                {serviceOrders.map((order) => (
                  <option key={order.id} value={order.id}>
                    {order.title} - {order.customer.name} {order.device ? `(${order.device.name})` : ""}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                name="title"
                required
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <RichTextEditor
                name="description"
                value={descriptionValue}
                onChange={setDescriptionValue}
                minHeight="100px"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="workPerformed" className="block text-sm font-medium text-gray-700">
                Work Performed
              </label>
              <RichTextEditor
                name="workPerformed"
                value={workPerformedValue}
                onChange={setWorkPerformedValue}
                minHeight="200px"
                placeholder="Describe the work that was performed..."
              />
            </div>

            <div className="mb-4">
              <label htmlFor="partsUsed" className="block text-sm font-medium text-gray-700">
                Parts Used (Legacy - For Backward Compatibility)
              </label>
              <textarea
                id="partsUsed"
                name="partsUsed"
                rows={2}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                placeholder="Use the Parts Selector below instead when possible..."
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Parts Selector
              </label>
              <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
                <p className="text-sm text-gray-600 mb-2">To select parts from inventory:</p>
                <Link
                  to="/inventory/parts-selector"
                  className="inline-block bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Open Parts Selector
                </Link>
                <p className="text-sm text-gray-500 mt-2">
                  A new feature is being implemented that will allow selecting parts from inventory.
                  This will automatically update stock levels and track part usage.
                </p>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="recommendations" className="block text-sm font-medium text-gray-700">
                Recommendations
              </label>
              <RichTextEditor
                name="recommendations"
                value={recommendationsValue}
                onChange={setRecommendationsValue}
                minHeight="150px"
                placeholder="Any recommendations for the customer..."
              />
            </div>

            <div className="mt-6 flex justify-end">
              <Link
                to="/service-reports"
                className="mr-4 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? "Creating..." : "Create Service Report"}
              </button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}