import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link } from "@remix-run/react";
import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useEffect, useRef, useState } from "react";
import { requireUserId } from "~/session.server";
import { getCalendarEntryById, updateCalendarEntry } from "~/services/calendar.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Checkbox } from "~/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { calendarEntryId } = params;

  if (!calendarEntryId) {
    throw new Response("Calendar Entry ID is required", { status: 400 });
  }

  const calendarEntryResponse = await getCalendarEntryById(calendarEntryId, userId);

  if (!calendarEntryResponse.success || !calendarEntryResponse.data) {
    throw new Response(calendarEntryResponse.error || "Calendar Entry not found", { status: 404 });
  }

  return json({ calendarEntry: calendarEntryResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { calendarEntryId } = params;

  if (!calendarEntryId) {
    throw new Response("Calendar Entry ID is required", { status: 400 });
  }

  const formData = await request.formData();

  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const location = formData.get("location") as string;
  const startDate = formData.get("startDate") as string;
  const startTime = formData.get("startTime") as string;
  const endDate = formData.get("endDate") as string;
  const endTime = formData.get("endTime") as string;
  const isAllDay = formData.get("isAllDay") === "on";
  const color = formData.get("color") as string;

  // Validate required fields
  const errors = {
    title: title ? null : "Title is required",
    startDate: startDate ? null : "Start date is required",
    endDate: endDate ? null : "End date is required",
    startTime: null as string | null,
    endTime: null as string | null,
    form: null as string | null,
  };

  if (!isAllDay) {
    errors.startTime = startTime ? null : "Start time is required";
    errors.endTime = endTime ? null : "End time is required";
  }

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Create start and end times
  let startDateTime, endDateTime;

  if (isAllDay) {
    startDateTime = new Date(`${startDate}T00:00:00`);
    endDateTime = new Date(`${endDate}T23:59:59`);
  } else {
    startDateTime = new Date(`${startDate}T${startTime}`);
    endDateTime = new Date(`${endDate}T${endTime}`);
  }

  // Validate that end time is after start time
  if (endDateTime <= startDateTime) {
    return json({
      errors: {
        ...errors,
        endDate: "End time must be after start time",
      },
      values: Object.fromEntries(formData),
    });
  }

  // Update calendar entry
  const calendarResponse = await updateCalendarEntry(
    calendarEntryId,
    {
      title,
      description: description || null,
      location: location || null,
      startTime: startDateTime,
      endTime: endDateTime,
      isAllDay,
      color: color || "#3B82F6", // Default to blue
    },
    userId
  );

  if (!calendarResponse.success) {
    return json({
      errors: {
        ...errors,
        form: calendarResponse.error || "Failed to update calendar entry",
      },
      values: Object.fromEntries(formData),
    });
  }

  return redirect(`/calendar/${calendarEntryId}`);
}

export default function EditCalendarEntryPage() {
  const { calendarEntry } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const titleRef = useRef<HTMLInputElement>(null);
  const [isAllDay, setIsAllDay] = useState(
    actionData?.values?.isAllDay === "on" || calendarEntry.isAllDay
  );

  // Format date for input fields
  const formatDateForInput = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Format time for input fields
  const formatTimeForInput = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toTimeString().substring(0, 5);
  };

  // Focus on the title field when there's an error
  useEffect(() => {
    if (actionData?.errors?.title) {
      titleRef.current?.focus();
    }
  }, [actionData]);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to={`/calendar/${calendarEntry.id}`} className="text-blue-500 hover:underline">
          ← Back to Event Details
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Event</CardTitle>
          <CardDescription>
            Update event details
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {actionData?.errors && 'form' in actionData.errors && actionData.errors.form && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {actionData.errors.form}
              </div>
            )}

            {/* Title field */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Title <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={titleRef}
                id="title"
                name="title"
                defaultValue={actionData?.values?.title as string || calendarEntry.title}
                aria-invalid={actionData?.errors?.title ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.title ? "title-error" : undefined
                }
              />
              {actionData?.errors?.title && (
                <p className="text-red-500 text-sm" id="title-error">
                  {actionData.errors.title}
                </p>
              )}
            </div>

            {/* Description field */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={3}
                defaultValue={actionData?.values?.description as string || calendarEntry.description || ""}
              />
            </div>

            {/* Location field */}
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                defaultValue={actionData?.values?.location as string || calendarEntry.location || ""}
              />
            </div>

            {/* All Day checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAllDay"
                name="isAllDay"
                checked={isAllDay}
                onCheckedChange={(checked) => setIsAllDay(checked === true)}
              />
              <Label htmlFor="isAllDay">All Day Event</Label>
            </div>

            {/* Date and Time fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">
                  Start Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  defaultValue={
                    actionData?.values?.startDate as string ||
                    formatDateForInput(calendarEntry.startTime)
                  }
                  aria-invalid={actionData?.errors?.startDate ? true : undefined}
                  aria-errormessage={
                    actionData?.errors?.startDate ? "startDate-error" : undefined
                  }
                />
                {actionData?.errors?.startDate && (
                  <p className="text-red-500 text-sm" id="startDate-error">
                    {actionData.errors.startDate}
                  </p>
                )}
              </div>

              {!isAllDay && (
                <div className="space-y-2">
                  <Label htmlFor="startTime">
                    Start Time <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="startTime"
                    name="startTime"
                    type="time"
                    defaultValue={
                      actionData?.values?.startTime as string ||
                      formatTimeForInput((calendarEntry as any).startTime)
                    }
                    aria-invalid={actionData?.errors?.startTime ? true : undefined}
                    aria-errormessage={
                      actionData?.errors?.startTime ? "startTime-error" : undefined
                    }
                  />
                  {actionData?.errors?.startTime && (
                    <p className="text-red-500 text-sm" id="startTime-error">
                      {actionData.errors.startTime}
                    </p>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="endDate">
                  End Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="endDate"
                  name="endDate"
                  type="date"
                  defaultValue={
                    actionData?.values?.endDate as string ||
                    formatDateForInput(calendarEntry.endTime)
                  }
                  aria-invalid={actionData?.errors?.endDate ? true : undefined}
                  aria-errormessage={
                    actionData?.errors?.endDate ? "endDate-error" : undefined
                  }
                />
                {actionData?.errors?.endDate && (
                  <p className="text-red-500 text-sm" id="endDate-error">
                    {actionData.errors.endDate}
                  </p>
                )}
              </div>

              {!isAllDay && (
                <div className="space-y-2">
                  <Label htmlFor="endTime">
                    End Time <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="endTime"
                    name="endTime"
                    type="time"
                    defaultValue={
                      actionData?.values?.endTime as string ||
                      formatTimeForInput((calendarEntry as any).endTime)
                    }
                    aria-invalid={actionData?.errors?.endTime ? true : undefined}
                    aria-errormessage={
                      actionData?.errors?.endTime ? "endTime-error" : undefined
                    }
                  />
                  {actionData?.errors?.endTime && (
                    <p className="text-red-500 text-sm" id="endTime-error">
                      {actionData.errors.endTime}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Color field */}
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select
                name="color"
                defaultValue={actionData?.values?.color as string || calendarEntry.color || "#3B82F6"}
              >
                <SelectTrigger id="color">
                  <SelectValue placeholder="Select a color" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="#3B82F6">Blue</SelectItem>
                  <SelectItem value="#10B981">Green</SelectItem>
                  <SelectItem value="#F59E0B">Yellow</SelectItem>
                  <SelectItem value="#EF4444">Red</SelectItem>
                  <SelectItem value="#8B5CF6">Purple</SelectItem>
                  <SelectItem value="#EC4899">Pink</SelectItem>
                  <SelectItem value="#6B7280">Gray</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to={`/calendar/${calendarEntry.id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
