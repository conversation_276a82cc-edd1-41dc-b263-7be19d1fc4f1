import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate, Link, Form } from "@remix-run/react";
import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { requireUserId } from "~/session.server";
import { getCalendarEntryById, deleteCalendarEntry } from "~/services/calendar.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { calendarEntryId } = params;

  if (!calendarEntryId) {
    throw new Response("Calendar Entry ID is required", { status: 400 });
  }

  const calendarEntryResponse = await getCalendarEntryById(calendarEntryId, userId);

  if (!calendarEntryResponse.success || !calendarEntryResponse.data) {
    throw new Response(calendarEntryResponse.error || "Calendar Entry not found", { status: 404 });
  }

  return json({ calendarEntry: calendarEntryResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { calendarEntryId } = params;

  if (!calendarEntryId) {
    throw new Response("Calendar Entry ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "delete") {
    const deleteResponse = await deleteCalendarEntry(calendarEntryId, userId);

    if (!deleteResponse.success) {
      return json({ error: deleteResponse.error }, { status: 400 });
    }

    return redirect("/calendar");
  }

  return json({ error: "Invalid intent" }, { status: 400 });
}

export default function CalendarEntryDetailPage() {
  const { calendarEntry } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // Handle delete confirmation
  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this event? This action cannot be undone.")) {
      const form = document.createElement("form");
      form.method = "post";
      form.appendChild(createHiddenInput("intent", "delete"));
      document.body.appendChild(form);
      form.submit();
    }
  };

  // Helper to create hidden input
  const createHiddenInput = (name: string, value: string) => {
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = name;
    input.value = value;
    return input;
  };

  // Format date and time for display
  const formatDateTime = (dateString: string, includeTime = true) => {
    if (!dateString) return "Not specified";
    const date = new Date(dateString);

    if (includeTime) {
      return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  };

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/calendar" className="text-blue-500 hover:underline">
          ← Back to Calendar
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{calendarEntry.title}</h1>
        <div className="flex gap-2">
          <Link to={`/calendar/${calendarEntry.id}/edit`}>
            <Button variant="outline">Edit</Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Event Details</CardTitle>
          {calendarEntry.isAllDay ? (
            <CardDescription>All-day event</CardDescription>
          ) : (
            <CardDescription>
              {formatDateTime(calendarEntry.startTime)} - {formatDateTime(calendarEntry.endTime)}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          {calendarEntry.description && (
            <div>
              <Label>Description</Label>
              <p className="whitespace-pre-line">{calendarEntry.description}</p>
            </div>
          )}

          {calendarEntry.location && (
            <div>
              <Label>Location</Label>
              <p>{calendarEntry.location}</p>
            </div>
          )}

          <div>
            <Label>When</Label>
            {calendarEntry.isAllDay ? (
              <p>
                {formatDateTime(calendarEntry.startTime, false)}
                {new Date(calendarEntry.startTime).toDateString() !== new Date(calendarEntry.endTime).toDateString() &&
                  ` - ${formatDateTime(calendarEntry.endTime, false)}`}
                <span className="ml-2 text-gray-500">(All day)</span>
              </p>
            ) : (
              <p>
                {formatDateTime(calendarEntry.startTime)} - {formatDateTime(calendarEntry.endTime)}
              </p>
            )}
          </div>

          <div>
            <Label>Created</Label>
            <p>{formatDateTime(calendarEntry.createdAt)}</p>
          </div>

          <div>
            <Label>Last Updated</Label>
            <p>{formatDateTime(calendarEntry.updatedAt)}</p>
          </div>
        </CardContent>
        <CardFooter>
          <Link to="/calendar">
            <Button variant="outline">Back to Calendar</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
