import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@remix-run/react";
import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { requireUserId } from "~/session.server";
import { getCustomerById, deleteCustomer } from "~/services/customer.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  const customerResponse = await getCustomerById(customerId, userId);

  if (!customerResponse.success || !customerResponse.data) {
    throw new Response(customerResponse.error || "Customer not found", { status: 404 });
  }

  return json({ customer: customerResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "delete") {
    const deleteResponse = await deleteCustomer(customerId, userId);

    if (!deleteResponse.success) {
      return json({ error: deleteResponse.error }, { status: 400 });
    }

    return redirect("/customers");
  }

  return json({ error: "Invalid intent" }, { status: 400 });
}

export default function CustomerDetailPage() {
  const { customer } = useLoaderData<typeof loader>();


  // Handle delete confirmation
  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this customer? This action cannot be undone.")) {
      const form = document.createElement("form");
      form.method = "post";
      form.appendChild(createHiddenInput("intent", "delete"));
      document.body.appendChild(form);
      form.submit();
    }
  };

  // Helper to create hidden input
  const createHiddenInput = (name: string, value: string) => {
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = name;
    input.value = value;
    return input;
  };

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/customers" className="text-blue-500 hover:underline">
          ← Back to Customers
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{customer.name}</h1>
        <div className="flex gap-2">
          <Link to={`/customers/${customer.id}/edit`}>
            <Button variant="outline">Edit</Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Name</Label>
              <p className="text-lg">{customer.name}</p>
            </div>

            <div>
              <Label>Email</Label>
              <p>{customer.email || "Not provided"}</p>
            </div>

            <div>
              <Label>Phone</Label>
              <p>{customer.phone || "Not provided"}</p>
            </div>

            <div>
              <Label>Address</Label>
              <p>{customer.address || "Not provided"}</p>
              {customer.city && customer.postalCode && (
                <p>
                  {customer.city}, {customer.postalCode}
                  {customer.country ? `, ${customer.country}` : ""}
                </p>
              )}
            </div>

            {customer.notes && (
              <div>
                <Label>Notes</Label>
                <p className="whitespace-pre-line">{customer.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Devices */}
        <Card>
          <CardHeader>
            <CardTitle>Devices</CardTitle>
            <CardDescription>
              {customer.devices.length} device(s) registered
            </CardDescription>
          </CardHeader>
          <CardContent>
            {customer.devices.length > 0 ? (
              <ul className="space-y-2">
                {customer.devices.map((device) => (
                  <li key={device.id} className="border-b pb-2">
                    <Link to={`/devices/${device.id}`} className="hover:underline">
                      <strong>{device.name}</strong>
                    </Link>
                    {device.model && <p className="text-sm text-gray-500">Model: {device.model}</p>}
                    {device.serialNumber && <p className="text-sm text-gray-500">S/N: {device.serialNumber}</p>}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500">No devices registered</p>
            )}
          </CardContent>
          <CardFooter>
            <Link to={`/devices/new?customerId=${customer.id}`}>
              <Button variant="outline">Add Device</Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Service Orders */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Service Orders</CardTitle>
            <CardDescription>
              {customer.serviceOrders.length} service order(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {customer.serviceOrders.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4">Title</th>
                      <th className="text-left py-2 px-4">Status</th>
                      <th className="text-left py-2 px-4">Device</th>
                      <th className="text-left py-2 px-4">Scheduled Date</th>
                      <th className="text-left py-2 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customer.serviceOrders.map((order) => (
                      <tr key={order.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-4">
                          <Link to={`/service-orders/${order.id}`} className="hover:underline">
                            {order.title}
                          </Link>
                        </td>
                        <td className="py-2 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="py-2 px-4">
                          {order.device ? (
                            <Link to={`/devices/${order.device.id}`} className="hover:underline">
                              {order.device.name}
                            </Link>
                          ) : (
                            "N/A"
                          )}
                        </td>
                        <td className="py-2 px-4">
                          {order.scheduledDate ? new Date(order.scheduledDate).toLocaleDateString() : "Not scheduled"}
                        </td>
                        <td className="py-2 px-4">
                          <Link to={`/service-orders/${order.id}/edit`}>
                            <Button variant="outline" size="sm">Edit</Button>
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No service orders</p>
            )}
          </CardContent>
          <CardFooter>
            <Link to={`/service-orders/new?customerId=${customer.id}`}>
              <Button variant="outline">Create Service Order</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
