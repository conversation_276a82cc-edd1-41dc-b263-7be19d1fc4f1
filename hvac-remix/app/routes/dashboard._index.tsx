import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Outlet, useLoader<PERSON><PERSON>, useNavigate, Link } from "@remix-run/react";
import { useState, useEffect } from "react";
import { requireUser } from "~/session.server";
import { getUserSettings } from "~/models/user-settings.server";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Avatar } from "~/components/ui/avatar";
import {
  Settings,
  Plus,
  Calendar,
  LayoutGrid,
  Bar<PERSON>hart,
  <PERSON><PERSON>hart as PieChartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  Gauge as GaugeIcon,
  Save,
  Undo
} from "lucide-react";
import { DashboardWidgetGrid } from "~/components/organisms/dashboard-widget-grid";
import { DashboardHeader } from "~/components/molecules/dashboard-header";
import { QuickActions } from "~/components/molecules/quick-actions";
import { convertToAppSettings } from "~/models/user-settings.server";
import type { UserRole } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Get user dashboard settings
  const dbSettings = await getUserSettings(user.id);
  const userSettings = dbSettings ? convertToAppSettings(dbSettings) : {
    theme: {
      theme: 'system',
      fontSize: 'medium',
      colorScheme: 'default'
    },
    dashboard: {
      showStats: true,
      showRecentOrders: true,
      showUpcomingEvents: true,
      showQuickActions: true,
      layout: 'grid',
      widgets: [
        { id: 'service-orders', position: 0, size: 'medium' },
        { id: 'device-types', position: 1, size: 'medium' },
        { id: 'revenue', position: 2, size: 'large' },
        { id: 'technician-performance', position: 3, size: 'medium' },
        { id: 'maintenance-forecast', position: 4, size: 'medium' }
      ],
      statsToShow: ['serviceOrders', 'customers', 'devices', 'revenue']
    },
    notifications: {
      email: true,
      inApp: true,
      push: false,
      serviceOrderUpdates: true,
      calendarReminders: true,
      systemUpdates: true
    }
  };

  // Get available widgets based on user role
  const availableWidgets = getAvailableWidgetsForRole(user.role as UserRole);

  return json({
    user,
    settings: userSettings,
    availableWidgets
  });
};

// Helper function to get available widgets based on user role
function getAvailableWidgetsForRole(role: UserRole) {
  const baseWidgets = [
    { id: 'service-orders', name: 'Zlecenia serwisowe', icon: BarChart, description: 'Liczba zleceń serwisowych w czasie' },
    { id: 'device-types', name: 'Typy urządzeń', icon: PieChartIcon, description: 'Podział urządzeń według typu' },
    { id: 'upcoming-service', name: 'Nadchodzące zlecenia', icon: Calendar, description: 'Nadchodzące zlecenia serwisowe' },
  ];

  const managerWidgets = [
    { id: 'revenue', name: 'Przychody', icon: LineChartIcon, description: 'Przychody w czasie' },
    { id: 'technician-performance', name: 'Wydajność techników', icon: BarChart, description: 'Porównanie wydajności techników' },
    { id: 'customer-satisfaction', name: 'Zadowolenie klientów', icon: GaugeIcon, description: 'Wskaźnik zadowolenia klientów' },
  ];

  const adminWidgets = [
    { id: 'system-health', name: 'Stan systemu', icon: GaugeIcon, description: 'Monitorowanie stanu systemu' },
    { id: 'user-activity', name: 'Aktywność użytkowników', icon: LineChartIcon, description: 'Aktywność użytkowników w czasie' },
  ];

  const technicianWidgets = [
    { id: 'my-performance', name: 'Moja wydajność', icon: LineChartIcon, description: 'Twoje statystyki wydajności' },
    { id: 'my-schedule', name: 'Mój harmonogram', icon: Calendar, description: 'Twój harmonogram na najbliższy czas' },
  ];

  switch (role) {
    case 'ADMIN':
      return [...baseWidgets, ...managerWidgets, ...adminWidgets];
    case 'MANAGER':
      return [...baseWidgets, ...managerWidgets];
    case 'TECHNICIAN':
      return [...baseWidgets, ...technicianWidgets];
    default:
      return baseWidgets;
  }
}

export default function DashboardIndexPage() {
  const { user, settings, availableWidgets } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [currentLayout, setCurrentLayout] = useState(settings.dashboard.widgets || []);
  const userRole = user.role as UserRole;
  const userName = user?.name ? String(user.name) : undefined;
  const avatarUrl = undefined; // Placeholder for user avatar

  // Handle customization mode toggle
  const toggleCustomizationMode = () => {
    setIsCustomizing(!isCustomizing);
    if (isCustomizing) {
      // Save layout changes when exiting customization mode
      // This would typically be an API call
      console.log('Saving layout:', currentLayout);
    }
  };

  // Handle adding a new widget
  const handleAddWidget = (widgetId: string) => {
    const newWidget = {
      id: widgetId,
      position: currentLayout.length,
      size: 'medium'
    };
    setCurrentLayout([...currentLayout, newWidget]);
  };

  return (
    <div className="container mx-auto py-8 px-4 min-h-screen">
      <DashboardHeader
        userName={userName}
        avatarUrl={avatarUrl}
        isCustomizing={isCustomizing}
        onCustomizeToggle={toggleCustomizationMode}
      />

      {/* Quick Actions */}
      {settings.dashboard.showQuickActions && !isCustomizing && (
        <QuickActions userRole={userRole} className="mb-8 animate-slide-up" />
      )}

      {/* Customization Controls */}
      {isCustomizing && (
        <Card className="mb-8 border-primary/20 shadow-md animate-scale">
          <CardHeader className="bg-primary/5 rounded-t-lg border-b border-primary/10">
            <CardTitle className="text-gradient-primary">Dostosuj swój dashboard</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-4 mb-6">
              {availableWidgets.map((widget, index) => (
                <Button
                  key={widget.id}
                  variant="outline"
                  onClick={() => handleAddWidget(widget.id)}
                  className="flex items-center gap-2 hover-lift border-primary/20 animate-fade-in"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <Plus className="h-4 w-4 text-primary" />
                  <widget.icon className="h-4 w-4 text-accent" />
                  <span>{widget.name}</span>
                </Button>
              ))}
            </div>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setCurrentLayout(settings.dashboard.widgets)}
                className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <Undo className="h-4 w-4 mr-2" />
                Resetuj
              </Button>
              <Button
                onClick={toggleCustomizationMode}
                className="bg-primary hover:bg-primary-hover transition-colors"
              >
                <Save className="h-4 w-4 mr-2" />
                Zapisz zmiany
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      <div className={`transition-opacity duration-500 ${isCustomizing ? 'opacity-100' : 'opacity-100'}`}>
        <DashboardWidgetGrid
          widgets={currentLayout}
          isCustomizing={isCustomizing}
          onLayoutChange={setCurrentLayout}
        />
      </div>

      {/* Outlet for nested routes */}
      <Outlet />
    </div>
  );
}
