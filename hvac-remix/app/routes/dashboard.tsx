import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@remix-run/react";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { requireUser } from "~/session.server";
import { Card } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON><PERSON><PERSON> } from "~/components/atoms/charts/bar-chart";
import { LineChart } from "~/components/atoms/charts/line-chart";
import { PieChart } from "~/components/atoms/charts/pie-chart";
import { GaugeChart } from "~/components/atoms/charts/gauge-chart";
import { TechnicianMobileDashboard } from "~/components/pages/technician-mobile-dashboard";
import { QuickActions } from "~/components/molecules/quick-actions";
import { PredictiveMaintenanceSummary } from "~/components/organisms/predictive-maintenance-summary";
import { StatCard } from "~/components/molecules/stat-card";
import { ChartCard } from "~/components/molecules/chart-card";
import { ServiceHistoryTimeline } from "~/components/organisms/service-history-timeline";
import { Avatar } from "../components/ui/avatar";
import type { UserRole } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Przykładowe dane dla wykresów - w przyszłości będą pobierane z bazy danych
  const serviceOrdersData = [
    { label: "Sty", value: 12 },
    { label: "Lut", value: 19 },
    { label: "Mar", value: 15 },
    { label: "Kwi", value: 22 },
    { label: "Maj", value: 28 },
    { label: "Cze", value: 24 },
  ];

  const deviceTypesData = [
    { label: "Klimatyzatory", value: 45 },
    { label: "Pompy ciepła", value: 28 },
    { label: "Wentylatory", value: 15 },
    { label: "Inne", value: 12 },
  ];

  const revenueData = [
    { label: "Sty", value: 15000 },
    { label: "Lut", value: 18000 },
    { label: "Mar", value: 22000 },
    { label: "Kwi", value: 19000 },
    { label: "Maj", value: 25000 },
    { label: "Cze", value: 28000 },
  ];

  // Przykładowe dane dla technika
  const todayServiceOrders = [
    {
      id: "so1",
      title: "Naprawa klimatyzacji",
      status: "PENDING",
      priority: "HIGH",
      scheduledDate: new Date().toISOString(),
      customer: {
        name: "Jan Kowalski",
        address: "ul. Przykładowa 1, Warszawa",
        phone: "123-456-789"
      },
      device: {
        id: "dev1",
        name: "Klimatyzator ścienny",
        model: "AC-2000",
        serialNumber: "SN12345"
      }
    },
    {
      id: "so2",
      title: "Przegląd okresowy",
      status: "IN_PROGRESS",
      priority: "MEDIUM",
      scheduledDate: new Date().toISOString(),
      customer: {
        name: "Anna Nowak",
        address: "ul. Testowa 5, Kraków",
        phone: "987-654-321"
      },
      device: {
        id: "dev2",
        name: "Pompa ciepła",
        model: "HP-500",
        serialNumber: "SN67890"
      }
    }
  ];

  const upcomingServiceOrders = [
    {
      id: "so3",
      title: "Instalacja klimatyzacji",
      status: "PENDING",
      priority: "MEDIUM",
      scheduledDate: new Date(Date.now() + 86400000).toISOString(), // tomorrow
      customer: {
        name: "Firma XYZ",
        address: "ul. Biznesowa 10, Poznań",
        phone: "111-222-333"
      },
      device: {
        id: "dev3",
        name: "Klimatyzator sufitowy",
        model: "AC-5000",
        serialNumber: "SN54321"
      }
    }
  ];

  const deviceAlerts = [
    {
      id: "alert1",
      deviceId: "dev1",
      deviceName: "Klimatyzator ścienny AC-2000",
      alertType: "HIGH",
      message: "Wykryto nieprawidłowe wibracje",
      createdAt: new Date().toISOString()
    },
    {
      id: "alert2",
      deviceId: "dev2",
      deviceName: "Pompa ciepła HP-500",
      alertType: "MEDIUM",
      message: "Spadek wydajności o 15%",
      createdAt: new Date().toISOString()
    }
  ];

  // Przykładowe dane dla historii serwisowej
  const serviceHistory = [
    {
      id: "sh1",
      date: new Date(Date.now() - 7 * 86400000).toISOString(), // 7 days ago
      type: "MAINTENANCE",
      title: "Przegląd okresowy",
      description: "Standardowy przegląd okresowy urządzenia",
      technician: "Adam Nowak",
      components: ["Filtr powietrza", "Czynnik chłodniczy"],
      status: "COMPLETED",
      serviceOrderId: "so-past-1"
    },
    {
      id: "sh2",
      date: new Date(Date.now() - 30 * 86400000).toISOString(), // 30 days ago
      type: "REPAIR",
      title: "Naprawa sprężarki",
      description: "Wymiana uszkodzonej sprężarki",
      technician: "Piotr Wiśniewski",
      components: ["Sprężarka", "Zawór rozprężny"],
      cost: 1200,
      status: "COMPLETED",
      serviceOrderId: "so-past-2"
    },
    {
      id: "sh3",
      date: new Date(Date.now() - 90 * 86400000).toISOString(), // 90 days ago
      type: "INSTALLATION",
      title: "Instalacja urządzenia",
      description: "Montaż i konfiguracja nowego urządzenia",
      technician: "Jan Kowalski",
      components: ["Jednostka wewnętrzna", "Jednostka zewnętrzna", "Sterownik"],
      cost: 2500,
      status: "COMPLETED",
      serviceOrderId: "so-past-3"
    }
  ];

  // Helper: Cast string to union type
  function asAlertType(type: string): 'HIGH' | 'MEDIUM' | 'LOW' {
    if (type === 'HIGH' || type === 'MEDIUM' || type === 'LOW') return type;
    return 'LOW';
  }
  function asServiceType(type: string): 'INSTALLATION' | 'MAINTENANCE' | 'REPAIR' | 'INSPECTION' | 'OTHER' {
    if (["INSTALLATION","MAINTENANCE","REPAIR","INSPECTION","OTHER"].includes(type)) return type as any;
    return 'OTHER';
  }
  // Map demo data to match expected types for components
  const mappedTodayServiceOrders = todayServiceOrders.map((o) => ({
    ...o,
    type: 'MAINTENANCE',
    customerId: '',
    createdAt: new Date(o.scheduledDate),
    updatedAt: new Date(o.scheduledDate),
    notes: null,
    description: '',
    status: o.status as any,
    offerId: null,
  }));
  const mappedUpcomingServiceOrders = upcomingServiceOrders.map((o) => ({
    ...o,
    type: 'MAINTENANCE',
    customerId: '',
    createdAt: new Date(o.scheduledDate),
    updatedAt: new Date(o.scheduledDate),
    notes: null,
    description: '',
    status: o.status as any,
    offerId: null,
  }));
  const mappedDeviceAlerts = deviceAlerts.map((a) => ({
    ...a,
    alertType: asAlertType(a.alertType),
  }));
  const mappedServiceHistory = serviceHistory.map((e) => ({
    ...e,
    type: asServiceType(e.type),
  }));
  return json({
    user,
    serviceOrdersData,
    deviceTypesData,
    revenueData,
    todayServiceOrders,
    upcomingServiceOrders,
    deviceAlerts,
    serviceHistory
  });
};

export default function DashboardPage() {
  const {
    user,
    serviceOrdersData,
    deviceTypesData,
    revenueData,
    todayServiceOrders,
    upcomingServiceOrders,
    deviceAlerts,
    serviceHistory
  } = useLoaderData<typeof loader>();
  const userRole = user.role as UserRole;

  // Avatar fallback for demo
  const avatarUrl = undefined;
  const userName = user?.name ? String(user.name) : undefined;

  // Render different dashboard based on user role
  if (userRole === 'TECHNICIAN') {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center gap-4 mb-8">
          <Avatar src={avatarUrl} alt={userName} size={56} />
          <div>
            <h1 className="text-2xl font-bold mb-1">Cześć, {userName || "Użytkowniku"} 👋</h1>
            <p className="text-gray-500 dark:text-gray-400 text-sm">Miłego dnia w HVAC CRM!</p>
          </div>
        </div>
        {/* Mobile-optimized dashboard for technicians */}
        <div className="block md:hidden">
          <TechnicianMobileDashboard
            serviceOrders={mappedTodayServiceOrders}
            upcomingServiceOrders={mappedUpcomingServiceOrders}
            deviceAlerts={mappedDeviceAlerts}
            userRole={userRole}
          />
        </div>
        {/* Desktop dashboard for technicians */}
        <div className="hidden md:block">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div className="lg:col-span-2">
              <QuickActions userRole={userRole} className="mb-6" />
              {/* Service Orders */}
              <Card className="p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">Dzisiejsze zlecenia</h2>
                <div className="space-y-4">
                  {mappedTodayServiceOrders.map((order: any) => (
                    <div key={order.id} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{order.title}</h3>
                        <div className="flex space-x-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            order.status === 'PENDING' ? 'bg-yellow-500 text-white' :
                            order.status === 'IN_PROGRESS' ? 'bg-blue-500 text-white' :
                            'bg-green-500 text-white'
                          }`}>
                            {order.status}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            order.priority === 'HIGH' ? 'bg-red-500 text-white' :
                            order.priority === 'MEDIUM' ? 'bg-orange-500 text-white' :
                            'bg-blue-500 text-white'
                          }`}>
                            {order.priority}
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        <span className="font-medium">Klient:</span> {order.customer?.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        <span className="font-medium">Adres:</span> {order.customer?.address}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        <span className="font-medium">Urządzenie:</span> {order.device?.name} {order.device?.model}
                      </p>
                    </div>
                  ))}
                </div>
              </Card>
              {/* Device Alerts */}
              {mappedDeviceAlerts.length > 0 && (
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Alerty urządzeń</h2>
                  <div className="space-y-4">
                    {mappedDeviceAlerts.map((alert: any) => (
                      <div key={alert.id} className="p-4 border-l-4 border-red-500 rounded-lg bg-red-50 dark:bg-red-900/20">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium">{alert.deviceName}</h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            alert.alertType === 'HIGH' ? 'bg-red-500 text-white' :
                            alert.alertType === 'MEDIUM' ? 'bg-orange-500 text-white' :
                            'bg-blue-500 text-white'
                          }`}>
                            {alert.alertType}
                          </span>
                        </div>
                        <p className="text-sm mb-2">{alert.message}</p>
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </div>
            <div>
              {/* Upcoming Service Orders */}
              <Card className="p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">Nadchodzące zlecenia</h2>
                <div className="space-y-4">
                  {mappedUpcomingServiceOrders.map((order: any) => (
                    <div key={order.id} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{order.title}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          order.priority === 'HIGH' ? 'bg-red-500 text-white' :
                          order.priority === 'MEDIUM' ? 'bg-orange-500 text-white' :
                          'bg-blue-500 text-white'
                        }`}>
                          {order.priority}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                        <span className="font-medium">Data:</span> {new Date(order.scheduledDate).toLocaleDateString()}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                        <span className="font-medium">Klient:</span> {order.customer?.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                        <span className="font-medium">Urządzenie:</span> {order.device?.name}
                      </p>
                    </div>
                  ))}
                </div>
              </Card>
              {/* Service History */}
              <ServiceHistoryTimeline
                events={mappedServiceHistory}
                maxItems={3}
                showViewAllLink={true}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default dashboard for other roles
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8 bg-gradient-to-r from-primary/10 to-accent/5 p-6 rounded-2xl border border-border/40 shadow-sm">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Avatar src={avatarUrl} alt={userName} size={64} className="border-2 border-primary/20 shadow-md" />
            <span className="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full bg-success border-2 border-white dark:border-gray-900"></span>
          </div>
          <div>
            <h1 className="text-2xl font-heading font-bold mb-1 text-gradient">Cześć, {userName || "Użytkowniku"} 👋</h1>
            <p className="text-muted-foreground text-sm">Miłego dnia w HVAC CRM! {new Date().toLocaleDateString('pl-PL', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </div>
        </div>
        <div className="flex items-center gap-2 mt-4 md:mt-0">
          <div className="bg-card/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-border/40 flex items-center gap-2 text-sm text-muted-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            <span>Dashboard</span>
          </div>
          <div className="bg-card/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-border/40 flex items-center gap-2 text-sm text-muted-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-accent">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            <span>{new Date().toLocaleDateString('pl-PL', { month: 'short', year: 'numeric' })}</span>
          </div>
        </div>
      </div>
      {/* Quick Actions */}
      <QuickActions userRole={userRole} className="mb-8" />
      {/* Statystyki */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <StatCard
          title="Zlecenia serwisowe"
          value="24"
          variant="primary"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
            </svg>
          }
          trend={{
            value: "12%",
            direction: "up",
            description: "w porównaniu do poprzedniego miesiąca"
          }}
        />
        <StatCard
          title="Klienci"
          value="156"
          variant="info"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          }
          trend={{
            value: "5%",
            direction: "up",
            description: "w porównaniu do poprzedniego miesiąca"
          }}
        />
        <StatCard
          title="Urządzenia"
          value="312"
          variant="success"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
              <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
            </svg>
          }
          trend={{
            value: "8%",
            direction: "up",
            description: "w porównaniu do poprzedniego miesiąca"
          }}
        />
        <StatCard
          title="Przychód"
          value="28 000 zł"
          variant="warning"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          }
          trend={{
            value: "15%",
            direction: "up",
            description: "w porównaniu do poprzedniego miesiąca"
          }}
        />
      </div>
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          {/* Wykresy */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <ChartCard
              title="Zlecenia serwisowe"
              description="Liczba zleceń serwisowych w ostatnich 6 miesiącach"
              variant="primary"
              action={
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/service-orders">
                    <span className="mr-1">Wszystkie</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </Link>
                </Button>
              }
            >
              <div className="h-64">
                <BarChart
                  data={serviceOrdersData}
                  height={240}
                  animated={true}
                />
              </div>
            </ChartCard>

            <ChartCard
              title="Typy urządzeń"
              description="Podział urządzeń według typu"
              variant="info"
              action={
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/devices">
                    <span className="mr-1">Wszystkie</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </Link>
                </Button>
              }
            >
              <div className="h-64 flex items-center justify-center">
                <PieChart
                  data={deviceTypesData}
                  size={220}
                />
              </div>
            </ChartCard>
          </div>

          {/* Dodatkowe wykresy dla menedżerów i administratorów */}
          {(userRole === 'MANAGER' || userRole === 'ADMIN') && (
            <ChartCard
              title="Przychód"
              description="Przychód w ostatnich 6 miesiącach"
              variant="success"
              className="mb-6"
              action={
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/reports/financial">
                    <span className="mr-1">Raporty</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </Link>
                </Button>
              }
            >
              <div className="h-64">
                <LineChart
                  data={revenueData}
                  height={240}
                />
              </div>
            </ChartCard>
          )}
          {/* Service History */}
          <ServiceHistoryTimeline
            events={mappedServiceHistory}
            maxItems={3}
            showViewAllLink={true}
          />
        </div>
        <div>
          {/* Predictive Maintenance Summary */}
          <ChartCard
            title="Predictive Maintenance"
            description="Analiza stanu urządzeń i predykcje awarii"
            variant="warning"
            className="mb-6"
            action={
              <Button variant="ghost" size="sm" asChild>
                <Link to="/maintenance/predictive">
                  <span className="mr-1">Szczegóły</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </Link>
              </Button>
            }
          >
            <div className="mb-6">
              <GaugeChart
                value={85}
                title="Ogólny stan floty"
                description="Bazując na predykcjach i telemetrii"
                colorScheme="health"
                size={180}
              />
            </div>
            <div className="space-y-3">
              <div className="flex items-center p-2 rounded-lg bg-destructive/10 text-sm">
                <span className="flex items-center justify-center w-8 h-8 rounded-full bg-destructive/20 text-destructive mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                </span>
                <span>3 urządzenia z wysokim ryzykiem awarii</span>
              </div>
              <div className="flex items-center p-2 rounded-lg bg-warning/10 text-sm">
                <span className="flex items-center justify-center w-8 h-8 rounded-full bg-warning/20 text-warning mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                </span>
                <span>7 urządzeń ze średnim ryzykiem awarii</span>
              </div>
              <div className="flex items-center p-2 rounded-lg bg-success/10 text-sm">
                <span className="flex items-center justify-center w-8 h-8 rounded-full bg-success/20 text-success mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </span>
                <span>302 urządzenia z niskim ryzykiem awarii</span>
              </div>
            </div>
          </ChartCard>
          {/* Ostatnie aktywności */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Ostatnie aktywności</h2>
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              <li className="py-3">
                <div className="flex justify-between">
                  <div>
                    <p className="font-medium">Nowe zlecenie serwisowe</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Klient: Jan Kowalski</p>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">2 godz. temu</span>
                </div>
              </li>
              <li className="py-3">
                <div className="flex justify-between">
                  <div>
                    <p className="font-medium">Zakończone zlecenie</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Technik: Adam Nowak</p>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">5 godz. temu</span>
                </div>
              </li>
              <li className="py-3">
                <div className="flex justify-between">
                  <div>
                    <p className="font-medium">Nowy klient</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Firma: ABC Sp. z o.o.</p>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">wczoraj</span>
                </div>
              </li>
            </ul>
          </Card>
        </div>
      </div>
    </div>
  );
}