import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, Link, useLoaderData, useActionData, useNavigation } from "@remix-run/react";
import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { ArchiveBoxIcon, ArrowPathIcon, ArrowsRightLeftIcon, BuildingStorefrontIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";
import { processBatchOperations } from "~/models/inventory.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get query parameters
  const url = new URL(request.url);
  const transactionType = url.searchParams.get("type") || "";
  const partId = url.searchParams.get("partId") || "";
  const locationId = url.searchParams.get("locationId") || "";
  const search = url.searchParams.get("search") || "";
  const period = url.searchParams.get("period") || "month";
  const page = Number(url.searchParams.get("page") || "1");
  const perPage = 20;

  // Get start date based on period
  let startDate: Date | null = null;
  if (period !== "all") {
    startDate = new Date();

    switch (period) {
      case "week":
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "month":
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "quarter":
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case "year":
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }
  }

  // Build filter condition
  const where: any = {};

  if (transactionType) {
    where.transactionType = transactionType;
  }

  if (partId) {
    where.partId = partId;
  }

  if (locationId) {
    where.locationId = locationId;
  }

  if (startDate) {
    where.transactionDate = {
      gte: startDate
    };
  }

  if (search) {
    where.OR = [
      {
        part: {
          name: {
            contains: search,
            mode: "insensitive"
          }
        }
      },
      {
        part: {
          partNumber: {
            contains: search,
            mode: "insensitive"
          }
        }
      },
      {
        reason: {
          contains: search,
          mode: "insensitive"
        }
      }
    ];
  }

  // Get transactions with pagination
  const [transactions, totalCount] = await Promise.all([
    prisma.inventoryTransaction.findMany({
      where,
      orderBy: {
        transactionDate: "desc"
      },
      include: {
        part: true,
        location: true,
        transferToLocation: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      skip: (page - 1) * perPage,
      take: perPage
    }),
    prisma.inventoryTransaction.count({ where })
  ]);

  // Get all inventory parts for form selectors
  const parts = await prisma.inventoryPart.findMany({
    where: {
      isActive: true
    },
    orderBy: {
      name: "asc"
    },
    select: {
      id: true,
      name: true,
      partNumber: true,
      currentStock: true,
      unitOfMeasure: true,
      category: true
    }
  });

  // Get all locations
  const locations = await prisma.location.findMany({
    where: {
      isActive: true,
      type: "INVENTORY"
    },
    orderBy: {
      name: "asc"
    }
  });

  return json({
    transactions,
    totalCount,
    currentPage: page,
    totalPages: Math.ceil(totalCount / perPage),
    parts,
    locations,
    filters: {
      transactionType,
      partId,
      locationId,
      search,
      period
    }
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  switch (action) {
    case "createTransaction": {
      const transactionType = formData.get("transactionType") as string;
      const partId = formData.get("partId") as string;
      const locationId = formData.get("locationId") as string;
      const quantity = Number(formData.get("quantity"));
      const reason = formData.get("reason") as string;
      const referenceId = formData.get("referenceId") as string || undefined;
      const referenceType = formData.get("referenceType") as string || undefined;

      if (!partId || !locationId || !quantity || !transactionType) {
        return json({ error: "Missing required fields" }, { status: 400 });
      }

      const operations = [{
        partId,
        quantity,
        locationId,
        reason,
        referenceId,
        referenceType
      }];

      try {
        await processBatchOperations(
          operations,
          transactionType as "RECEIPT" | "USAGE" | "ADJUSTMENT",
          userId,
          reason
        );

        return json({ success: true, message: `Transaction completed successfully` });
      } catch (error: any) {
        return json({ error: error.message }, { status: 400 });
      }
    }

    case "createTransfer": {
      const partId = formData.get("partId") as string;
      const fromLocationId = formData.get("fromLocationId") as string;
      const toLocationId = formData.get("toLocationId") as string;
      const quantity = Number(formData.get("quantity"));
      const reason = formData.get("reason") as string;

      if (!partId || !fromLocationId || !toLocationId || !quantity) {
        return json({ error: "Missing required fields" }, { status: 400 });
      }

      if (fromLocationId === toLocationId) {
        return json({ error: "Source and destination locations cannot be the same" }, { status: 400 });
      }

      try {
        // Use the model function for transfers
        await prisma.$transaction(async (tx) => {
          // Check if source has enough stock
          const sourceLocation = await tx.inventoryLocation.findUnique({
            where: {
              partId_locationId: {
                partId,
                locationId: fromLocationId
              }
            }
          });

          if (!sourceLocation || sourceLocation.quantity < quantity) {
            throw new Error(`Not enough stock in source location. Available: ${sourceLocation?.quantity || 0}`);
          }

          // Remove from source location
          await tx.inventoryLocation.update({
            where: {
              partId_locationId: {
                partId,
                locationId: fromLocationId
              }
            },
            data: {
              quantity: {
                decrement: quantity
              }
            }
          });

          // Add to destination location
          await tx.inventoryLocation.upsert({
            where: {
              partId_locationId: {
                partId,
                locationId: toLocationId
              }
            },
            create: {
              partId,
              locationId: toLocationId,
              quantity
            },
            update: {
              quantity: {
                increment: quantity
              }
            }
          });

          // Record transfer transaction
          await tx.inventoryTransaction.create({
            data: {
              partId,
              quantity: 0, // Net quantity change is 0 for transfer
              transactionType: "TRANSFER",
              locationId: fromLocationId, // Source location
              transferToLocationId: toLocationId, // Destination location
              reason,
              userId
            }
          });
        });

        return json({ success: true, message: "Transfer completed successfully" });
      } catch (error: any) {
        return json({ error: error.message }, { status: 400 });
      }
    }

    default:
      return json({ error: "Invalid action" }, { status: 400 });
  }
}

export default function InventoryTransactions() {
  const {
    transactions,
    totalCount,
    currentPage,
    totalPages,
    parts,
    locations,
    filters
  } = useLoaderData<typeof loader>();

  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Inventory Transactions</h1>
        <div className="flex gap-2">
          <Link
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Inventory List
          </Link>
        </div>
      </div>

      {/* Transaction Filters */}
      <div className="rounded-md border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 p-4">
          <Form method="get" className="flex flex-wrap gap-3 sm:items-center">
            <div className="w-full sm:w-auto">
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">Transaction Type</label>
              <select
                id="type"
                name="type"
                defaultValue={filters.transactionType}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">All Types</option>
                <option value="RECEIPT">Receipts</option>
                <option value="USAGE">Usage</option>
                <option value="TRANSFER">Transfers</option>
                <option value="ADJUSTMENT">Adjustments</option>
              </select>
            </div>

            <div className="w-full sm:w-auto">
              <label htmlFor="partId" className="block text-sm font-medium text-gray-700">Part</label>
              <select
                id="partId"
                name="partId"
                defaultValue={filters.partId}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">All Parts</option>
                {parts.map(part => (
                  <option key={part.id} value={part.id}>
                    {part.name} {part.partNumber ? `(${part.partNumber})` : ''}
                  </option>
                ))}
              </select>
            </div>

            <div className="w-full sm:w-auto">
              <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">Location</label>
              <select
                id="locationId"
                name="locationId"
                defaultValue={filters.locationId}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">All Locations</option>
                {locations.map(location => (
                  <option key={location.id} value={location.id}>{location.name}</option>
                ))}
              </select>
            </div>

            <div className="w-full sm:w-auto">
              <label htmlFor="period" className="block text-sm font-medium text-gray-700">Time Period</label>
              <select
                id="period"
                name="period"
                defaultValue={filters.period}
                className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="quarter">Last 90 Days</option>
                <option value="year">Last 365 Days</option>
                <option value="all">All Time</option>
              </select>
            </div>

            <div className="w-full sm:w-auto">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700">Search</label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <div className="relative flex flex-grow items-stretch">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    type="text"
                    name="search"
                    id="search"
                    defaultValue={filters.search}
                    className="block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="Search transactions"
                  />
                </div>
              </div>
            </div>

            <div className="mt-auto w-full pt-4 sm:w-auto sm:pt-0">
              <button
                type="submit"
                className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
              >
                Filter Results
              </button>
            </div>
          </Form>
        </div>
      </div>

      {/* Action buttons for new transactions */}
      <div className="flex flex-wrap gap-2 sm:flex-nowrap">
        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white p-4 shadow">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Add Stock</h3>
          <p className="mt-1 text-sm text-gray-500">Record receipt of inventory items</p>
          <button
            type="button"
            className="mt-3 inline-flex items-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700"
            onClick={() => document.getElementById('receiptModal')?.classList.remove('hidden')}
          >
            <ArrowPathIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Add Stock
          </button>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white p-4 shadow">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Remove Stock</h3>
          <p className="mt-1 text-sm text-gray-500">Record usage of inventory items</p>
          <button
            type="button"
            className="mt-3 inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700"
            onClick={() => document.getElementById('usageModal')?.classList.remove('hidden')}
          >
            <ArrowPathIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Remove Stock
          </button>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white p-4 shadow">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Transfer Stock</h3>
          <p className="mt-1 text-sm text-gray-500">Move inventory between locations</p>
          <button
            type="button"
            className="mt-3 inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
            onClick={() => document.getElementById('transferModal')?.classList.remove('hidden')}
          >
            <ArrowsRightLeftIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Transfer Stock
          </button>
        </div>

        <div className="overflow-hidden rounded-lg border border-gray-200 bg-white p-4 shadow">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory Adjustment</h3>
          <p className="mt-1 text-sm text-gray-500">Correct inventory discrepancies</p>
          <button
            type="button"
            className="mt-3 inline-flex items-center rounded-md border border-transparent bg-yellow-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-yellow-700"
            onClick={() => document.getElementById('adjustmentModal')?.classList.remove('hidden')}
          >
            <BuildingStorefrontIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Adjust Stock
          </button>
        </div>
      </div>

      {/* Action result message */}
      {actionData?.error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{actionData.error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {actionData?.success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Success</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>{actionData.message}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Transaction List */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Transaction History</h3>
          <p className="mt-1 text-sm text-gray-500">
            Showing {totalCount === 0 ? '0' : ((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} transactions
          </p>
          <div className="mt-5 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Part
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Reference/Reason
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {transactions.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      No transactions found matching the criteria.
                    </td>
                  </tr>
                ) : transactions.map((transaction) => (
                  <tr key={transaction.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {new Date(transaction.transactionDate).toLocaleString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        transaction.transactionType === 'RECEIPT' ? 'bg-green-100 text-green-800' :
                        transaction.transactionType === 'USAGE' ? 'bg-red-100 text-red-800' :
                        transaction.transactionType === 'TRANSFER' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {transaction.transactionType}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${transaction.partId}`} className="text-blue-600 hover:underline">
                        {transaction.part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.transactionType === 'TRANSFER' ? 'Transfer' : Math.abs(transaction.quantity)} {transaction.part.unitOfMeasure}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.location?.name || 'N/A'}
                      {transaction.transferToLocationId && (
                        <> → {transaction.transferToLocation?.name || 'Unknown'}</>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.user?.name || 'System'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {transaction.referenceType && transaction.referenceId ? (
                        <Link
                          to={`/${transaction.referenceType.toLowerCase()}s/${transaction.referenceId}`}
                          className="text-blue-600 hover:underline"
                        >
                          {transaction.referenceType.replace('_', ' ')} #{transaction.referenceId.substring(0, 8)}
                        </Link>
                      ) :
                      transaction.reason || 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
              <div className="flex flex-1 justify-between sm:hidden">
                <Link
                  to={`?page=${Math.max(1, currentPage - 1)}`}
                  className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Previous
                </Link>
                <Link
                  to={`?page=${Math.min(totalPages, currentPage + 1)}`}
                  className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Next
                </Link>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{totalCount === 0 ? '0' : ((currentPage - 1) * 20) + 1}</span> to <span className="font-medium">{Math.min(currentPage * 20, totalCount)}</span> of{' '}
                    <span className="font-medium">{totalCount}</span> results
                  </p>
                </div>
                <div>
                  <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <Link
                      to={`?page=${Math.max(1, currentPage - 1)}`}
                      className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                    >
                      <span className="sr-only">Previous</span>
                      ←
                    </Link>

                    {[...Array(Math.min(5, totalPages))].map((_, i) => {
                      let pageNum = i + 1;
                      if (totalPages > 5) {
                        if (currentPage > 3) {
                          pageNum = currentPage - 3 + i;
                        }
                        if (pageNum > totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        }
                      }

                      return (
                        <Link
                          key={pageNum}
                          to={`?page=${pageNum}`}
                          className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${pageNum === currentPage
                            ? 'z-10 bg-blue-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                            : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0'
                          }`}
                        >
                          {pageNum}
                        </Link>
                      );
                    })}

                    <Link
                      to={`?page=${Math.min(totalPages, currentPage + 1)}`}
                      className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                    >
                      <span className="sr-only">Next</span>
                      →
                    </Link>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Receipt Modal */}
      <div id="receiptModal" className="fixed inset-0 z-10 hidden overflow-y-auto">
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity" aria-hidden="true">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

          <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
            <Form method="post">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Add Stock (Receipt)</h3>
                <div className="mt-4 space-y-4">
                  <input type="hidden" name="action" value="createTransaction" />
                  <input type="hidden" name="transactionType" value="RECEIPT" />

                  <div>
                    <label htmlFor="partId" className="block text-sm font-medium text-gray-700">Part</label>
                    <select
                      id="partId"
                      name="partId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a part</option>
                      {parts.map(part => (
                        <option key={part.id} value={part.id}>
                          {part.name} {part.partNumber ? `(${part.partNumber})` : ''} - Current: {part.currentStock} {part.unitOfMeasure}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">Location</label>
                    <select
                      id="locationId"
                      name="locationId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      name="quantity"
                      id="quantity"
                      min="1"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700">Reason / Source</label>
                    <input
                      type="text"
                      name="reason"
                      id="reason"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Purchase, Return, etc."
                    />
                  </div>

                  <div>
                    <label htmlFor="referenceType" className="block text-sm font-medium text-gray-700">Reference Type (Optional)</label>
                    <select
                      id="referenceType"
                      name="referenceType"
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">None</option>
                      <option value="PURCHASE_ORDER">Purchase Order</option>
                      <option value="SERVICE_ORDER">Service Order</option>
                      <option value="INVOICE">Invoice</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="referenceId" className="block text-sm font-medium text-gray-700">Reference ID (Optional)</label>
                    <input
                      type="text"
                      name="referenceId"
                      id="referenceId"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="PO123, SO456, etc."
                    />
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Processing...' : 'Add Stock'}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => document.getElementById('receiptModal')?.classList.add('hidden')}
                >
                  Cancel
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>

      {/* Usage Modal */}
      <div id="usageModal" className="fixed inset-0 z-10 hidden overflow-y-auto">
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity" aria-hidden="true">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

          <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
            <Form method="post">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Remove Stock (Usage)</h3>
                <div className="mt-4 space-y-4">
                  <input type="hidden" name="action" value="createTransaction" />
                  <input type="hidden" name="transactionType" value="USAGE" />

                  <div>
                    <label htmlFor="partId" className="block text-sm font-medium text-gray-700">Part</label>
                    <select
                      id="partId"
                      name="partId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a part</option>
                      {parts.map(part => (
                        <option key={part.id} value={part.id}>
                          {part.name} {part.partNumber ? `(${part.partNumber})` : ''} - Available: {part.currentStock} {part.unitOfMeasure}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">Location</label>
                    <select
                      id="locationId"
                      name="locationId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      name="quantity"
                      id="quantity"
                      min="1"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700">Reason / Purpose</label>
                    <input
                      type="text"
                      name="reason"
                      id="reason"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Service, Internal use, etc."
                    />
                  </div>

                  <div>
                    <label htmlFor="referenceType" className="block text-sm font-medium text-gray-700">Reference Type (Optional)</label>
                    <select
                      id="referenceType"
                      name="referenceType"
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">None</option>
                      <option value="SERVICE_ORDER">Service Order</option>
                      <option value="MAINTENANCE">Maintenance</option>
                      <option value="PROJECT">Project</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="referenceId" className="block text-sm font-medium text-gray-700">Reference ID (Optional)</label>
                    <input
                      type="text"
                      name="referenceId"
                      id="referenceId"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="SO123, MAINT456, etc."
                    />
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Processing...' : 'Remove Stock'}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => document.getElementById('usageModal')?.classList.add('hidden')}
                >
                  Cancel
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>

      {/* Transfer Modal */}
      <div id="transferModal" className="fixed inset-0 z-10 hidden overflow-y-auto">
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity" aria-hidden="true">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

          <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
            <Form method="post">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Transfer Stock Between Locations</h3>
                <div className="mt-4 space-y-4">
                  <input type="hidden" name="action" value="createTransfer" />

                  <div>
                    <label htmlFor="partId" className="block text-sm font-medium text-gray-700">Part</label>
                    <select
                      id="partId"
                      name="partId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a part</option>
                      {parts.map(part => (
                        <option key={part.id} value={part.id}>
                          {part.name} {part.partNumber ? `(${part.partNumber})` : ''} - Available: {part.currentStock} {part.unitOfMeasure}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="fromLocationId" className="block text-sm font-medium text-gray-700">From Location</label>
                    <select
                      id="fromLocationId"
                      name="fromLocationId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select source location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="toLocationId" className="block text-sm font-medium text-gray-700">To Location</label>
                    <select
                      id="toLocationId"
                      name="toLocationId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select destination location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      name="quantity"
                      id="quantity"
                      min="1"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700">Reason for Transfer</label>
                    <input
                      type="text"
                      name="reason"
                      id="reason"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Rebalancing, Service preparation, etc."
                    />
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Processing...' : 'Transfer Stock'}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => document.getElementById('transferModal')?.classList.add('hidden')}
                >
                  Cancel
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>

      {/* Adjustment Modal */}
      <div id="adjustmentModal" className="fixed inset-0 z-10 hidden overflow-y-auto">
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <div className="fixed inset-0 transition-opacity" aria-hidden="true">
            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

          <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
            <Form method="post">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Inventory Adjustment</h3>
                <div className="mt-4 space-y-4">
                  <input type="hidden" name="action" value="createTransaction" />
                  <input type="hidden" name="transactionType" value="ADJUSTMENT" />

                  <div>
                    <label htmlFor="partId" className="block text-sm font-medium text-gray-700">Part</label>
                    <select
                      id="partId"
                      name="partId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a part</option>
                      {parts.map(part => (
                        <option key={part.id} value={part.id}>
                          {part.name} {part.partNumber ? `(${part.partNumber})` : ''} - Current: {part.currentStock} {part.unitOfMeasure}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">Location</label>
                    <select
                      id="locationId"
                      name="locationId"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a location</option>
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>{location.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Adjustment Type</label>
                    <div className="mt-2">
                      <div className="flex items-center">
                        <input
                          id="add"
                          name="adjustmentType"
                          type="radio"
                          value="add"
                          defaultChecked
                          className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                          onChange={() => {
                            const quantityInput = document.getElementById('quantity') as HTMLInputElement;
                            if (quantityInput) {
                              quantityInput.min = '1';
                            }
                          }}
                        />
                        <label htmlFor="add" className="ml-3 block text-sm font-medium text-gray-700">
                          Add to inventory (positive adjustment)
                        </label>
                      </div>
                      <div className="mt-2 flex items-center">
                        <input
                          id="remove"
                          name="adjustmentType"
                          type="radio"
                          value="remove"
                          className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                          onChange={() => {
                            const quantityInput = document.getElementById('quantity') as HTMLInputElement;
                            if (quantityInput) {
                              quantityInput.min = '1';
                            }
                          }}
                        />
                        <label htmlFor="remove" className="ml-3 block text-sm font-medium text-gray-700">
                          Remove from inventory (negative adjustment)
                        </label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      name="quantity"
                      id="quantity"
                      min="1"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700">Reason for Adjustment</label>
                    <input
                      type="text"
                      name="reason"
                      id="reason"
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Physical count, Damaged goods, etc."
                    />
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="submit"
                  className="inline-flex w-full justify-center rounded-md border border-transparent bg-yellow-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={isSubmitting}
                  onClick={() => {
                    // Modify the quantity value based on the adjustment type
                    const form = document.getElementById('adjustmentModal')?.querySelector('form');
                    if (form) {
                      const adjustmentType = form.querySelector('input[name="adjustmentType"]:checked') as HTMLInputElement;
                      const quantityInput = form.querySelector('#quantity') as HTMLInputElement;

                      if (adjustmentType && quantityInput && adjustmentType.value === 'remove') {
                        // Create a hidden input to hold the actual quantity value
                        const hiddenQuantity = document.createElement('input');
                        hiddenQuantity.type = 'hidden';
                        hiddenQuantity.name = 'quantity';
                        hiddenQuantity.value = quantityInput.value;

                        // Replace the original quantity input name to prevent it from being submitted
                        quantityInput.name = 'display_quantity';

                        form.appendChild(hiddenQuantity);
                      }
                    }
                  }}
                >
                  {isSubmitting ? 'Processing...' : 'Submit Adjustment'}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => document.getElementById('adjustmentModal')?.classList.add('hidden')}
                >
                  Cancel
                </button>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}