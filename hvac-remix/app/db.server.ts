import { PrismaClient } from "@prisma/client";
// import { supabase } from "./supabase.server"; // Re-exported below
import { singleton } from "./singleton.server";
import { withConnection } from "~/utils/db-pool.server";

// Create Prisma client with logging configuration
const prisma = singleton("prisma", () =>
  new PrismaClient({
    log: process.env.NODE_ENV === "development"
      ? ["query", "error", "warn"]
      : ["error"],
    // Enable query events for performance monitoring
    __internal: {
      hooks: {
        beforeQuery: [
          (params: any) => {
            if (process.env.NODE_ENV === "production" && params.duration > 500) {
              console.warn(`Slow query (${params.duration}ms): ${params.query}`);
            }
          }
        ]
      }
    } as any
  })
);

// Connect to the database
prisma.$connect().catch((error) => {
  console.error("Failed to connect to the database:", error);
  process.exit(1);
});

// Handle graceful shutdown
process.on("beforeExit", async () => {
  await prisma.$disconnect();
});

/**
 * Execute a database transaction with automatic retry
 *
 * @param fn - Function to execute within the transaction
 * @param maxRetries - Maximum number of retries
 * @returns Transaction result
 */
export async function transaction<T>(
  fn: (tx: PrismaClient) => Promise<T>,
  maxRetries = 3
): Promise<T> {
  let retries = 0;

  while (true) {
    try {
      return await prisma.$transaction(fn) as T;
    } catch (error: any) {
      // Check if it's a transient error that can be retried
      const isTransientError =
        error.code === "P1001" || // Connection error
        error.code === "P1002" || // Timeout error
        error.code === "P1008" || // Operation timed out
        error.code === "P1017";   // Server closed the connection

      if (!isTransientError || retries >= maxRetries) {
        throw error;
      }

      // Exponential backoff
      const delay = Math.pow(2, retries) * 100;
      await new Promise(resolve => setTimeout(resolve, delay));

      retries++;
    }
  }
}

// Export prisma as db for compatibility with existing code
export { prisma, prisma as db, withConnection };
export { supabase } from "./supabase.server";
