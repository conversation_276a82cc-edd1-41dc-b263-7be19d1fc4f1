/**
 * By default, <PERSON> will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/docs/en/main/file-conventions/entry.server
 */

import { PassThrough } from "node:stream";

import type { EntryContext } from "@remix-run/node";
import { createReadableStreamFromReadable } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { isbot } from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import { initializeGraphQLServer } from "./services/graphql.server";
import { initializeQdrantCollections } from "./services/qdrant.server";
import { initializeBielikLLM } from "./services/bielik.server";
import { initializeEventHandlers } from "./services/eventHandlers.server";
import { initializeServerMonitoring, captureException } from "./utils/monitoring.server";
import { securityHeaders, isStaticAssetRequest } from "./utils/security-headers.server";

const ABORT_DELAY = 5_000;

// Initialize services
let servicesInitialized = false;

async function initializeServices() {
  if (!servicesInitialized) {
    try {
      // Initialize monitoring first to catch any initialization errors
      initializeServerMonitoring();

      // Initialize Bielik LLM service
      const bielikInitialized = await initializeBielikLLM();
      console.log('Bielik LLM initialization:', bielikInitialized ? 'successful' : 'failed');

      // Initialize Qdrant collections (depends on Bielik for embedding dimension)
      await initializeQdrantCollections();

      // Initialize GraphQL server (after other services are ready)
      await initializeGraphQLServer();

      // Initialize Event Handlers
      await initializeEventHandlers();

      servicesInitialized = true;
      console.log('All services initialized successfully');
    } catch (error) {
      captureException(error instanceof Error ? error : new Error(String(error)));
      console.error('Error initializing services:', error);
    }
  }
}

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  // Initialize services on first request
  await initializeServices();

  // Add security headers
  const url = new URL(request.url);
  const isStaticAsset = url.pathname.includes("/build/") ||
                       url.pathname.includes("/assets/") ||
                       /\.(js|css|png|jpg|jpeg|svg|ico|woff|woff2|ttf|otf|webp|webm|mp4)$/.test(url.pathname);

  // Add all security headers
  Object.entries(securityHeaders).forEach(([header, value]) => {
    // Skip Cache-Control for static assets
    if (isStaticAsset && header === "Cache-Control") {
      return;
    }

    responseHeaders.set(header, value);
  });

  // Add cache control for static assets
  if (isStaticAsset) {
    responseHeaders.set("Cache-Control", "public, max-age=31536000, immutable");
  }

  // Add server and powered-by headers
  responseHeaders.set("X-Powered-By", "HVAC CRM");

  return isbot(request.headers.get("user-agent"))
    ? handleBotRequest(
        request,
        responseStatusCode,
        responseHeaders,
        remixContext,
      )
    : handleBrowserRequest(
        request,
        responseStatusCode,
        responseHeaders,
        remixContext,
      );
}

function handleBotRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  return new Promise((resolve, reject) => {
    const { abort, pipe } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={ABORT_DELAY}
      />,
      {
        onAllReady() {
          const body = new PassThrough();

          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(createReadableStreamFromReadable(body), {
              headers: responseHeaders,
              status: responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          captureException(error instanceof Error ? error : new Error(String(error)));
          console.error(error);
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

function handleBrowserRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
) {
  return new Promise((resolve, reject) => {
    const { abort, pipe } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
        abortDelay={ABORT_DELAY}
      />,
      {
        onShellReady() {
          const body = new PassThrough();

          responseHeaders.set("Content-Type", "text/html");

          resolve(
            new Response(createReadableStreamFromReadable(body), {
              headers: responseHeaders,
              status: responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          captureException(error instanceof Error ? error : new Error(String(error)));
          console.error(error);
          responseStatusCode = 500;
        },
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}
