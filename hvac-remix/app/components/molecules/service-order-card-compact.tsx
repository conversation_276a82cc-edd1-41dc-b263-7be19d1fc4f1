import { Link } from "@remix-run/react"
import { Card } from "~/components/ui/card"

import { Badge } from "~/components/ui/badge"
import type { ServiceOrder, Device } from "@prisma/client"

interface ServiceOrderCardCompactProps {
  serviceOrder: ServiceOrder & {
    device?: Device & { name: string, model: string },
    customer?: { name: string, address: string }
  }
  showActions?: boolean
}

export function ServiceOrderCardCompact({
  serviceOrder,
  showActions = true
}: ServiceOrderCardCompactProps) {
  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-500 text-white'
      case 'IN_PROGRESS':
        return 'bg-blue-500 text-white'
      case 'COMPLETED':
        return 'bg-green-500 text-white'
      case 'CANCELLED':
        return 'bg-red-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  // Priority color mapping
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-500 text-white'
      case 'MEDIUM':
        return 'bg-orange-500 text-white'
      case 'LOW':
        return 'bg-blue-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  return (
    <Card className="p-3 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium truncate">{serviceOrder.title}</h3>
        <div className="flex space-x-1 flex-shrink-0">
          <Badge className={getStatusColor(serviceOrder.status)}>
            {serviceOrder.status}
          </Badge>
          <Badge className={getPriorityColor(serviceOrder.priority)}>
            {serviceOrder.priority}
          </Badge>
        </div>
      </div>

      <div className="text-sm mb-2">
        <p className="text-gray-500 dark:text-gray-400 truncate">
          <span className="font-medium">Klient:</span> {serviceOrder.customer?.name}
        </p>
        {serviceOrder.device && (
          <p className="text-gray-500 dark:text-gray-400 truncate">
            <span className="font-medium">Urządzenie:</span> {serviceOrder.device.name} {serviceOrder.device.model}
          </p>
        )}
        <p className="text-gray-500 dark:text-gray-400">
          <span className="font-medium">Data:</span> {serviceOrder.scheduledDate ? new Date(serviceOrder.scheduledDate).toLocaleDateString() : 'Nie zaplanowano'} {serviceOrder.scheduledDate ? new Date(serviceOrder.scheduledDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : ''}
        </p>
      </div>

      {showActions && (
        <div className="flex space-x-2">
          <Link
            to={`/service-orders/${serviceOrder.id}`}
            className="flex-1 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
          >
            Szczegóły
          </Link>
          {serviceOrder.status === 'PENDING' && (
            <Link
              to={`/service-orders/${serviceOrder.id}/edit?status=IN_PROGRESS`}
              className="flex-1 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
            >
              Rozpocznij
            </Link>
          )}
          {serviceOrder.status === 'IN_PROGRESS' && (
            <Link
              to={`/service-orders/${serviceOrder.id}/edit?status=COMPLETED`}
              className="flex-1 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
            >
              Zakończ
            </Link>
          )}
        </div>
      )}
    </Card>
  )
}
