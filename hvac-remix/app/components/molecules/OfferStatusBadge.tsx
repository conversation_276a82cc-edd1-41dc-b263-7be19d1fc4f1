
import { Badge } from "~/components/ui/badge";

type OfferStatus = "DRAFT" | "SENT" | "ACCEPTED" | "REJECTED" | "EXPIRED";

interface OfferStatusBadgeProps {
  status: OfferStatus | string;
}

export function OfferStatusBadge({ status }: OfferStatusBadgeProps) {
  let variant:
    | "default"
    | "secondary"
    | "destructive"
    | "outline"
    | "success"
    | "warning" = "default";

  switch (status) {
    case "DRAFT":
      variant = "secondary";
      break;
    case "SENT":
      variant = "warning";
      break;
    case "ACCEPTED":
      variant = "success";
      break;
    case "REJECTED":
      variant = "destructive";
      break;
    case "EXPIRED":
      variant = "outline";
      break;
    default:
      variant = "default";
  }

  return (
    <Badge variant={variant}>
      {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
    </Badge>
  );
}