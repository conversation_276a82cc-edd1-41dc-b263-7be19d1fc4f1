
import { Link } from "@remix-run/react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "~/components/ui/card";
import { OfferStatusBadge } from "./OfferStatusBadge";
import { formatCurrency, formatDate } from "~/utils";

interface OfferCardProps {
  id: string;
  title: string;
  status: string;
  totalAmount: number;
  validUntil?: Date | null;
  customerName: string;
  createdAt: Date;
}

export function OfferCard({
  id,
  title,
  status,
  totalAmount,
  validUntil,
  customerName,
  createdAt,
}: OfferCardProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-medium">
            <Link to={`/offers/${id}`} className="hover:underline">
              {title}
            </Link>
          </CardTitle>
          <OfferStatusBadge status={status} />
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Customer:</span>
            <span className="text-sm font-medium">{customerName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Amount:</span>
            <span className="text-sm font-medium">{formatCurrency(totalAmount)}</span>
          </div>
          {validUntil && (
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Valid until:</span>
              <span className="text-sm font-medium">{formatDate(validUntil)}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        <div className="w-full flex justify-between items-center">
          <span className="text-xs text-muted-foreground">
            Created: {formatDate(createdAt)}
          </span>
          <Link
            to={`/offers/${id}`}
            className="text-xs text-primary hover:underline"
          >
            View details
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}