import { Link } from "@remix-run/react"
import { Button } from "~/components/ui/button"

interface TimelineEventProps {
  icon: React.ReactNode
  title: string
  description: string
  date: string
  badge?: React.ReactNode
  isLast?: boolean
  metadata?: Record<string, string>
  link?: string
  linkText?: string
}

export function TimelineEvent({
  icon,
  title,
  description,
  date,
  badge,
  isLast = false,
  metadata,
  link,
  linkText = "Szczegóły"
}: TimelineEventProps) {
  return (
    <div className="relative pl-10">
      {/* Timeline line */}
      {!isLast && (
        <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-border/60 dark:bg-border/40" />
      )}

      {/* Event icon */}
      <div className="absolute left-0 top-0">
        {icon}
      </div>

      {/* Event content */}
      <div className="bg-card p-4 rounded-xl border border-border/40 shadow-sm hover:shadow-md transition-all duration-200 hover:translate-y-[-1px]">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium font-heading">{title}</h3>
          {badge}
        </div>

        <p className="text-sm text-muted-foreground mb-3">
          {description}
        </p>

        <div className="text-xs text-muted-foreground/70 mb-2 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-muted-foreground/60" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          {date}
        </div>

        {/* Additional metadata if available */}
        {metadata && Object.keys(metadata).length > 0 && (
          <div className="mt-3 pt-3 border-t border-border/30">
            {Object.entries(metadata).map(([key, value]) => (
              <div key={key} className="flex justify-between text-sm mb-1 last:mb-0">
                <span className="text-muted-foreground">{key}:</span>
                <span className="font-medium">{value}</span>
              </div>
            ))}
          </div>
        )}

        {/* Link if available */}
        {link && (
          <div className="mt-3">
            <Button
              asChild
              variant="outline"
              size="sm"
              className="w-full"
            >
              <Link to={link}>
                <span className="mr-1">{linkText}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
