import { Avatar } from "~/components/ui/avatar";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Settings, Edit, Check, LayoutDashboard, Moon, Sun } from "lucide-react";
import { DateRangePicker } from "~/components/molecules/date-range-picker";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

interface DashboardHeaderProps {
  userName?: string;
  avatarUrl?: string;
  isCustomizing: boolean;
  onCustomizeToggle: () => void;
}

export function DashboardHeader({
  userName,
  avatarUrl,
  isCustomizing,
  onCustomizeToggle
}: DashboardHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8 animate-fade-in">
      <div className="flex items-center gap-4">
        <div className="relative">
          <Avatar src={avatarUrl} alt={userName} size={56} />
          <span className="absolute bottom-0 right-0 w-3.5 h-3.5 bg-success rounded-full border-2 border-white dark:border-gray-800"></span>
        </div>
        <div>
          <h1 className="text-2xl font-bold mb-1 text-gradient-primary">
            Cześć, {userName || "Użytkowniku"} 👋
          </h1>
          <p className="text-gray-500 dark:text-gray-400 text-sm animate-slide-in-right">
            Miłego dnia w HVAC CRM!
          </p>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 animate-slide-in-left">
        {!isCustomizing && (
          <DateRangePicker
            className="w-full sm:w-auto glass"
            onChange={(range) => {
              console.log('Date range changed:', range);
            }}
          />
        )}

        <Button
          variant={isCustomizing ? "default" : "outline"}
          onClick={onCustomizeToggle}
          className={`flex items-center gap-2 hover-lift ${isCustomizing ? 'bg-primary hover:bg-primary-hover' : 'border-primary/30'}`}
        >
          {isCustomizing ? (
            <>
              <Check className="h-4 w-4 animate-pulse" />
              Zakończ edycję
            </>
          ) : (
            <>
              <Edit className="h-4 w-4" />
              Dostosuj dashboard
            </>
          )}
        </Button>

        {!isCustomizing && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="hover-glow">
                <Settings className="h-5 w-5 transition-transform duration-300 hover:rotate-90" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 animate-scale">
              <DropdownMenuLabel>Ustawienia dashboardu</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <LayoutDashboard className="mr-2 h-4 w-4" />
                <span>Układ dashboardu</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Sun className="mr-2 h-4 w-4" />
                <span>Tryb jasny</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Moon className="mr-2 h-4 w-4" />
                <span>Tryb ciemny</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
}
