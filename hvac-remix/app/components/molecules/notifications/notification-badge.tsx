
import { Badge } from "~/components/ui/badge";

interface NotificationBadgeProps {
  count: number;
  maxCount?: number;
}

/**
 * Komponent odznaki powiadomień
 * Wyświetla liczbę nieprzeczytanych powiadomień
 */
export function NotificationBadge({ count, maxCount = 99 }: NotificationBadgeProps) {
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  if (count === 0) {
    return null;
  }

  return (
    <Badge
      className="bg-red-500 hover:bg-red-600 text-white absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center px-1 text-xs rounded-full"
    >
      {displayCount}
    </Badge>
  );
}