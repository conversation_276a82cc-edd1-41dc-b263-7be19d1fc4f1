
import { Link } from "@remix-run/react";
import type { Notification, NotificationPriority } from "~/types/shared";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: string) => void;
}

/**
 * Komponent pojedynczego powiadomienia
 */
export function NotificationItem({ notification, onMarkAsRead }: NotificationItemProps) {
  // Funkcja do określania klasy koloru na podstawie priorytetu
  const getPriorityColorClass = (priority: NotificationPriority): string => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500';
      case 'medium':
        return 'border-l-4 border-yellow-500';
      case 'low':
      default:
        return 'border-l-4 border-blue-500';
    }
  };

  // Formatowanie daty
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pl-PL', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handleMarkAsRead = () => {
    if (onMarkAsRead && notification.status === 'unread') {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <div
      className={`p-4 bg-white dark:bg-gray-800 rounded-md shadow-sm mb-2 ${
        getPriorityColorClass(notification.priority)
      } ${
        notification.status === 'unread' ? 'bg-blue-50 dark:bg-gray-700' : ''
      }`}
      onClick={handleMarkAsRead}
    >
      <div className="flex justify-between items-start">
        <h4 className="font-medium text-sm">{notification.title}</h4>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {formatDate(notification.createdAt)}
        </span>
      </div>

      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
        {notification.message}
      </p>

      {notification.link && (
        <div className="mt-2">
          <Link
            to={notification.link}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            Zobacz szczegóły
          </Link>
        </div>
      )}
    </div>
  );
}