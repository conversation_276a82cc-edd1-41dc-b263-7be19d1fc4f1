import React, { useState } from "react";
import { Form, useSubmit, useNavigation } from "@remix-run/react";
import { Switch } from "~/components/ui/switch";
import { Card } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Checkbox } from "~/components/ui/checkbox";
import type { AdvancedNotificationSettings, NotificationChannel } from "~/types/shared";

interface NotificationPreferencesProps {
  preferences: AdvancedNotificationSettings;
}

/**
 * Komponent preferencji powiadomień
 * Pozwala użytkownikowi dostosować ustawienia powiadomień
 */
export function NotificationPreferences({ preferences }: NotificationPreferencesProps) {
  const submit = useSubmit();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  // <PERSON> dla zaawansowanych ustawień
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Stan dla kanałów powiadomień
  const [emailEnabled, setEmailEnabled] = useState(preferences.email);
  const [smsEnabled, setSmsEnabled] = useState(preferences.sms || false);
  const [inAppEnabled, setInAppEnabled] = useState(preferences.inApp);
  const [pushEnabled, setPushEnabled] = useState(preferences.push);

  // Stan dla typów powiadomień
  const [serviceOrdersEnabled, setServiceOrdersEnabled] = useState(preferences.serviceOrderUpdates);
  const [calendarEnabled, setCalendarEnabled] = useState(preferences.calendarReminders);
  const [systemEnabled, setSystemEnabled] = useState(preferences.systemUpdates);
  const [deviceAlertsEnabled, setDeviceAlertsEnabled] = useState(preferences.deviceAlerts);
  const [newMessagesEnabled, setNewMessagesEnabled] = useState(preferences.newMessages);

  // Stan dla kanałów dla poszczególnych typów powiadomień
  const [serviceOrderChannels, setServiceOrderChannels] = useState<NotificationChannel[]>(
    preferences.serviceOrderChannels || ['IN_APP']
  );
  const [calendarChannels, setCalendarChannels] = useState<NotificationChannel[]>(
    preferences.calendarReminderChannels || ['IN_APP']
  );
  const [systemChannels, setSystemChannels] = useState<NotificationChannel[]>(
    preferences.systemUpdateChannels || ['IN_APP']
  );
  const [deviceAlertChannels, setDeviceAlertChannels] = useState<NotificationChannel[]>(
    preferences.deviceAlertChannels || ['IN_APP']
  );
  const [messageChannels, setMessageChannels] = useState<NotificationChannel[]>(
    preferences.newMessageChannels || ['IN_APP']
  );

  // Dane kontaktowe
  const [notificationEmail, setNotificationEmail] = useState(preferences.notificationEmail || '');
  const [notificationPhone, setNotificationPhone] = useState(preferences.notificationPhone || '');

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Dodaj kanały dla poszczególnych typów powiadomień do formularza
    const formData = new FormData(event.currentTarget);

    // Dodaj zaawansowane ustawienia jeśli są włączone
    if (showAdvanced) {
      formData.append('advanced', 'true');

      // Dodaj kanały dla poszczególnych typów powiadomień
      formData.append('serviceOrderChannels', JSON.stringify(serviceOrderChannels));
      formData.append('calendarReminderChannels', JSON.stringify(calendarChannels));
      formData.append('systemUpdateChannels', JSON.stringify(systemChannels));
      formData.append('deviceAlertChannels', JSON.stringify(deviceAlertChannels));
      formData.append('newMessageChannels', JSON.stringify(messageChannels));

      // Dodaj dane kontaktowe
      formData.append('notificationEmail', notificationEmail);
      formData.append('notificationPhone', notificationPhone);
    }

    submit(formData, { method: "post" });
  };

  // Funkcja pomocnicza do aktualizacji kanałów
  const updateChannel = (
    channels: NotificationChannel[],
    setChannels: React.Dispatch<React.SetStateAction<NotificationChannel[]>>,
    channel: NotificationChannel,
    checked: boolean
  ) => {
    if (checked) {
      setChannels([...channels, channel]);
    } else {
      setChannels(channels.filter(c => c !== channel));
    }
  };

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Ustawienia powiadomień</h2>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="basic" onClick={() => setShowAdvanced(false)}>Podstawowe</TabsTrigger>
          <TabsTrigger value="advanced" onClick={() => setShowAdvanced(true)}>Zaawansowane</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <Form method="post" onSubmit={handleSubmit}>
            <input type="hidden" name="action" value="updatePreferences" />

            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Kanały powiadomień</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia e-mail</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia na swój adres e-mail
                    </p>
                  </div>
                  <Switch
                    name="email"
                    checked={emailEnabled}
                    onCheckedChange={setEmailEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia SMS</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia SMS na swój telefon
                    </p>
                  </div>
                  <Switch
                    name="sms"
                    checked={smsEnabled}
                    onCheckedChange={setSmsEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia w aplikacji</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia w interfejsie aplikacji
                    </p>
                  </div>
                  <Switch
                    name="inApp"
                    checked={inAppEnabled}
                    onCheckedChange={setInAppEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia push</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia push na urządzeniu mobilnym
                    </p>
                  </div>
                  <Switch
                    name="push"
                    checked={pushEnabled}
                    onCheckedChange={setPushEnabled}
                    value="true"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Typy powiadomień</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Aktualizacje zleceń serwisowych</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o nowych i zaktualizowanych zleceniach
                    </p>
                  </div>
                  <Switch
                    name="serviceOrderUpdates"
                    checked={serviceOrdersEnabled}
                    onCheckedChange={setServiceOrdersEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Przypomnienia kalendarza</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Przypomnienia o nadchodzących wydarzeniach
                    </p>
                  </div>
                  <Switch
                    name="calendarReminders"
                    checked={calendarEnabled}
                    onCheckedChange={setCalendarEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Alerty urządzeń</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o alertach z urządzeń
                    </p>
                  </div>
                  <Switch
                    name="deviceAlerts"
                    checked={deviceAlertsEnabled}
                    onCheckedChange={setDeviceAlertsEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Nowe wiadomości</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o nowych wiadomościach
                    </p>
                  </div>
                  <Switch
                    name="newMessages"
                    checked={newMessagesEnabled}
                    onCheckedChange={setNewMessagesEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Aktualizacje systemowe</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o aktualizacjach i zmianach w systemie
                    </p>
                  </div>
                  <Switch
                    name="systemUpdates"
                    checked={systemEnabled}
                    onCheckedChange={setSystemEnabled}
                    value="true"
                  />
                </div>
              </div>

              <div className="pt-4">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Zapisywanie..." : "Zapisz preferencje"}
                </Button>
              </div>
            </div>
          </Form>
        </TabsContent>

        <TabsContent value="advanced">
          <Form method="post" onSubmit={handleSubmit}>
            <input type="hidden" name="action" value="updatePreferences" />
            <input type="hidden" name="advanced" value="true" />

            {/* Podstawowe ustawienia kanałów */}
            <div className="space-y-6 mb-8">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Kanały powiadomień</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia e-mail</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia na swój adres e-mail
                    </p>
                  </div>
                  <Switch
                    name="email"
                    checked={emailEnabled}
                    onCheckedChange={setEmailEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia SMS</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia SMS na swój telefon
                    </p>
                  </div>
                  <Switch
                    name="sms"
                    checked={smsEnabled}
                    onCheckedChange={setSmsEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia w aplikacji</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia w interfejsie aplikacji
                    </p>
                  </div>
                  <Switch
                    name="inApp"
                    checked={inAppEnabled}
                    onCheckedChange={setInAppEnabled}
                    value="true"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Powiadomienia push</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia push na urządzeniu mobilnym
                    </p>
                  </div>
                  <Switch
                    name="push"
                    checked={pushEnabled}
                    onCheckedChange={setPushEnabled}
                    value="true"
                  />
                </div>
              </div>

              {/* Dane kontaktowe */}
              <div className="space-y-4 border-t pt-4">
                <h3 className="text-lg font-medium">Dane kontaktowe do powiadomień</h3>

                <div className="space-y-2">
                  <Label htmlFor="notificationEmail">Alternatywny adres e-mail do powiadomień</Label>
                  <Input
                    id="notificationEmail"
                    name="notificationEmail"
                    type="email"
                    placeholder="Pozostaw puste, aby używać głównego adresu e-mail"
                    value={notificationEmail}
                    onChange={(e) => setNotificationEmail(e.target.value)}
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Jeśli nie podasz adresu, powiadomienia będą wysyłane na Twój główny adres e-mail
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notificationPhone">Numer telefonu do powiadomień SMS</Label>
                  <Input
                    id="notificationPhone"
                    name="notificationPhone"
                    type="tel"
                    placeholder="np. +48123456789"
                    value={notificationPhone}
                    onChange={(e) => setNotificationPhone(e.target.value)}
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Podaj numer telefonu w formacie międzynarodowym (np. +48123456789)
                  </p>
                </div>
              </div>
            </div>

            {/* Zaawansowane ustawienia typów powiadomień */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium border-t pt-4">Zaawansowane ustawienia typów powiadomień</h3>

              {/* Zlecenia serwisowe */}
              <div className="space-y-2 border p-4 rounded-md">
                <div className="flex items-center justify-between">
                  <p className="font-medium">Aktualizacje zleceń serwisowych</p>
                  <Switch
                    name="serviceOrderUpdates"
                    checked={serviceOrdersEnabled}
                    onCheckedChange={setServiceOrdersEnabled}
                    value="true"
                  />
                </div>

                {serviceOrdersEnabled && (
                  <div className="mt-2 pl-4 border-l-2 border-gray-200">
                    <p className="text-sm mb-2">Wybierz kanały powiadomień:</p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="serviceOrder-inapp"
                          checked={serviceOrderChannels.includes('IN_APP')}
                          onCheckedChange={(checked) =>
                            updateChannel(serviceOrderChannels, setServiceOrderChannels, 'IN_APP', checked as boolean)
                          }
                          disabled={!inAppEnabled}
                        />
                        <label
                          htmlFor="serviceOrder-inapp"
                          className={!inAppEnabled ? "text-gray-400" : ""}
                        >
                          W aplikacji
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="serviceOrder-email"
                          checked={serviceOrderChannels.includes('EMAIL')}
                          onCheckedChange={(checked) =>
                            updateChannel(serviceOrderChannels, setServiceOrderChannels, 'EMAIL', checked as boolean)
                          }
                          disabled={!emailEnabled}
                        />
                        <label
                          htmlFor="serviceOrder-email"
                          className={!emailEnabled ? "text-gray-400" : ""}
                        >
                          E-mail
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="serviceOrder-sms"
                          checked={serviceOrderChannels.includes('SMS')}
                          onCheckedChange={(checked) =>
                            updateChannel(serviceOrderChannels, setServiceOrderChannels, 'SMS', checked as boolean)
                          }
                          disabled={!smsEnabled}
                        />
                        <label
                          htmlFor="serviceOrder-sms"
                          className={!smsEnabled ? "text-gray-400" : ""}
                        >
                          SMS
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="serviceOrder-push"
                          checked={serviceOrderChannels.includes('PUSH')}
                          onCheckedChange={(checked) =>
                            updateChannel(serviceOrderChannels, setServiceOrderChannels, 'PUSH', checked as boolean)
                          }
                          disabled={!pushEnabled}
                        />
                        <label
                          htmlFor="serviceOrder-push"
                          className={!pushEnabled ? "text-gray-400" : ""}
                        >
                          Push
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Pozostałe typy powiadomień - podobna struktura */}
              {/* Kalendarz */}
              <div className="space-y-2 border p-4 rounded-md">
                <div className="flex items-center justify-between">
                  <p className="font-medium">Przypomnienia kalendarza</p>
                  <Switch
                    name="calendarReminders"
                    checked={calendarEnabled}
                    onCheckedChange={setCalendarEnabled}
                    value="true"
                  />
                </div>

                {calendarEnabled && (
                  <div className="mt-2 pl-4 border-l-2 border-gray-200">
                    <p className="text-sm mb-2">Wybierz kanały powiadomień:</p>
                    <div className="space-y-2">
                      {/* Podobne checkboxy jak dla zleceń serwisowych */}
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="calendar-inapp"
                          checked={calendarChannels.includes('IN_APP')}
                          onCheckedChange={(checked) =>
                            updateChannel(calendarChannels, setCalendarChannels, 'IN_APP', checked as boolean)
                          }
                          disabled={!inAppEnabled}
                        />
                        <label
                          htmlFor="calendar-inapp"
                          className={!inAppEnabled ? "text-gray-400" : ""}
                        >
                          W aplikacji
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="calendar-email"
                          checked={calendarChannels.includes('EMAIL')}
                          onCheckedChange={(checked) =>
                            updateChannel(calendarChannels, setCalendarChannels, 'EMAIL', checked as boolean)
                          }
                          disabled={!emailEnabled}
                        />
                        <label
                          htmlFor="calendar-email"
                          className={!emailEnabled ? "text-gray-400" : ""}
                        >
                          E-mail
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="calendar-sms"
                          checked={calendarChannels.includes('SMS')}
                          onCheckedChange={(checked) =>
                            updateChannel(calendarChannels, setCalendarChannels, 'SMS', checked as boolean)
                          }
                          disabled={!smsEnabled}
                        />
                        <label
                          htmlFor="calendar-sms"
                          className={!smsEnabled ? "text-gray-400" : ""}
                        >
                          SMS
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="calendar-push"
                          checked={calendarChannels.includes('PUSH')}
                          onCheckedChange={(checked) =>
                            updateChannel(calendarChannels, setCalendarChannels, 'PUSH', checked as boolean)
                          }
                          disabled={!pushEnabled}
                        />
                        <label
                          htmlFor="calendar-push"
                          className={!pushEnabled ? "text-gray-400" : ""}
                        >
                          Push
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Przycisk zapisz */}
              <div className="pt-4">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Zapisywanie..." : "Zapisz zaawansowane preferencje"}
                </Button>
              </div>
            </div>
          </Form>
        </TabsContent>
      </Tabs>
    </Card>
  );
}
