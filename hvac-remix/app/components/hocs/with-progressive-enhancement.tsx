import React from 'react';
import { useServiceAvailability, ServiceType } from '~/contexts/service-availability';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { ServerOff } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { ServiceAvailabilityIndicator } from '~/components/atoms/service-availability-indicator';

interface WithProgressiveEnhancementOptions {
  requiredServices: ServiceType[];
  fallbackComponent?: React.ComponentType<any> | null;
  showServiceStatus?: boolean;
  alertTitle?: string;
  alertDescription?: string;
}

/**
 * Higher-order component that adds progressive enhancement to components
 * that depend on Python backend services
 *
 * @param Component The component to enhance
 * @param options Configuration options
 * @returns Enhanced component with progressive enhancement
 */
export function withProgressiveEnhancement<P extends object>(
  Component: React.ComponentType<P>,
  options: WithProgressiveEnhancementOptions
): React.FC<P> {
  const {
    requiredServices,
    fallbackComponent: FallbackComponent = null,
    showServiceStatus = true,
    alertTitle = 'Service Unavailable',
    alertDescription = 'Some features are currently unavailable due to backend service issues.',
  } = options;

  // Create the enhanced component
  const EnhancedComponent: React.FC<P> = (props) => {
    const { services, checkService } = useServiceAvailability();

    // Check if all required services are available
    const areAllServicesAvailable = requiredServices.every(
      (service) => services[service].status === 'available'
    );

    // If all services are available, render the component
    if (areAllServicesAvailable) {
      return <Component {...props} />;
    }

    // If a fallback component is provided, render it
    if (FallbackComponent) {
      return <FallbackComponent {...props} />;
    }

    // Otherwise, render an alert
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <ServerOff className="h-4 w-4" />
          <AlertTitle>{alertTitle}</AlertTitle>
          <AlertDescription>
            {alertDescription}
          </AlertDescription>
        </Alert>

        {showServiceStatus && (
          <div className="p-4 border rounded-md bg-background">
            <h3 className="text-sm font-medium mb-3">Service Status</h3>
            <div className="space-y-2">
              {requiredServices.map((service) => (
                <div key={service} className="flex items-center justify-between">
                  <span className="text-sm">
                    {service === ServiceType.BIELIK_LLM
                      ? 'Bielik LLM'
                      : service === ServiceType.OCR
                      ? 'OCR Service'
                      : 'Predictive Maintenance'}
                  </span>
                  <div className="flex items-center gap-2">
                    <ServiceAvailabilityIndicator serviceType={service} />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 px-2"
                      onClick={() => checkService(service)}
                    >
                      Retry
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Set display name for debugging
  EnhancedComponent.displayName = `withProgressiveEnhancement(${
    Component.displayName || Component.name || 'Component'
  })`;

  return EnhancedComponent;
}
