// React import not needed with modern JSX transform

export function Avatar({ src, alt, size = 48 }: { src?: string; alt?: string; size?: number }) {
  return (
    <div
      className="rounded-full bg-gradient-to-br from-blue-200/60 to-blue-100/60 shadow-md flex items-center justify-center overflow-hidden"
      style={{ width: size, height: size }}
    >
      {src ? (
        <img src={src} alt={alt} className="w-full h-full object-cover" />
      ) : (
        <span className="text-blue-700 text-xl font-bold">🙂</span>
      )}
    </div>
  );
}
