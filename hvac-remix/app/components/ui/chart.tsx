// React import not needed with modern JSX transform
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, Y<PERSON><PERSON><PERSON> } from "recharts";

interface ChartData {
  date: string;
  count: number;
}

interface ChartProps {
  data: ChartData[];
  title: string;
}

export function ServiceTrendChart({ data, title }: ChartProps) {
  return (
    <div className="h-[300px] w-full">
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="count" stroke="#007bff" activeDot={{ r: 8 }} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
