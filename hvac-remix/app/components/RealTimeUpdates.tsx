import { useEffect, useState } from 'react';
import { subscribeToChanges } from '~/utils/supabase';

interface RealTimeUpdatesProps {
  table: string;
  title?: string;
}

export default function RealTimeUpdates({ table, title = 'Real-time Updates' }: RealTimeUpdatesProps) {
  const [events, setEvents] = useState<any[]>([]);

  useEffect(() => {
    // Subscribe to changes
    const unsubscribe = subscribeToChanges(table, (payload) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      setEvents((prev) => [
        {
          id: Date.now(),
          eventType,
          newRecord,
          oldRecord,
          timestamp: new Date().toISOString(),
        },
        ...prev.slice(0, 9), // Keep only the 10 most recent events
      ]);
    });

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, [table]);

  return (
    <div className="bg-white shadow rounded-lg p-4">
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      
      {events.length === 0 ? (
        <p className="text-gray-500">No events yet. Make changes to see real-time updates.</p>
      ) : (
        <ul className="space-y-3">
          {events.map((event) => (
            <li key={event.id} className="border-l-4 pl-3 py-2" style={{ 
              borderColor: 
                event.eventType === 'INSERT' ? 'green' : 
                event.eventType === 'UPDATE' ? 'blue' : 
                event.eventType === 'DELETE' ? 'red' : 'gray' 
            }}>
              <div className="flex justify-between">
                <span className="font-medium">
                  {event.eventType === 'INSERT' ? 'New record created' : 
                   event.eventType === 'UPDATE' ? 'Record updated' : 
                   event.eventType === 'DELETE' ? 'Record deleted' : event.eventType}
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(event.timestamp).toLocaleTimeString()}
                </span>
              </div>
              
              <div className="mt-1 text-sm">
                {event.eventType === 'INSERT' && (
                  <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                    {JSON.stringify(event.newRecord, null, 2)}
                  </pre>
                )}
                
                {event.eventType === 'UPDATE' && (
                  <div>
                    <div className="text-xs font-medium mb-1">Changes:</div>
                    <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(
                        Object.entries(event.newRecord || {}).reduce((acc: Record<string, any>, [key, value]) => {
                          if (event.oldRecord && (event.oldRecord as Record<string, any>)[key] !== value) {
                            acc[key] = {
                              from: (event.oldRecord as Record<string, any>)[key],
                              to: value,
                            };
                          }
                          return acc;
                        }, {}),
                        null,
                        2
                      )}
                    </pre>
                  </div>
                )}
                
                {event.eventType === 'DELETE' && (
                  <pre className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                    {JSON.stringify(event.oldRecord, null, 2)}
                  </pre>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}