import { cn } from "~/lib/utils";

interface BarChartProps {
  data: {
    label: string;
    value: number;
    color?: string;
  }[];
  height?: number;
  title?: string;
  description?: string;
  className?: string;
  showValues?: boolean;
  showLabels?: boolean;
  animated?: boolean;
}

/**
 * Enhanced bar chart component with animations and better styling
 */
export function BarChart({
  data,
  height = 200,
  title,
  description,
  className,
  showValues = true,
  showLabels = true,
  animated = true
}: BarChartProps) {
  const maxValue = Math.max(...data.map(item => item.value));

  // Default colors if not specified
  const defaultColors = [
    "bg-gradient-to-t from-primary/80 to-primary/60",
    "bg-gradient-to-t from-accent/80 to-accent/60",
    "bg-gradient-to-t from-info/80 to-info/60",
    "bg-gradient-to-t from-success/80 to-success/60",
    "bg-gradient-to-t from-warning/80 to-warning/60",
  ];

  return (
    <div className={cn("w-full", className)}>
      {title && <h3 className="text-lg font-medium mb-2">{title}</h3>}
      {description && <p className="text-sm text-muted-foreground mb-4">{description}</p>}

      <div className="w-full" style={{ height: `${height}px` }}>
        <div className="flex h-full items-end space-x-3 px-1">
          {data.map((item, index) => {
            const percentage = (item.value / maxValue) * 100;
            const barColor = item.color || defaultColors[index % defaultColors.length];

            return (
              <div key={index} className="flex flex-col items-center flex-1 group">
                <div className="relative w-full flex flex-col items-center justify-end h-full">
                  {showValues && (
                    <div className="absolute -top-6 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      {item.value}
                    </div>
                  )}
                  <div
                    className={cn(
                      "w-full rounded-t-lg shadow-md relative overflow-hidden",
                      barColor,
                      animated && "animate-in fade-in-50 slide-in-from-bottom-5 duration-700"
                    )}
                    style={{
                      height: `${percentage}%`,
                      transitionDelay: `${index * 100}ms`,
                    }}
                  >
                    <div className="absolute inset-0 bg-white/10 w-full h-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  </div>
                </div>
                {showLabels && (
                  <div className="text-xs mt-2 text-center font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-200">
                    {item.label}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}