

interface PieChartProps {
  data: {
    label: string;
    value: number;
    color?: string;
  }[];
  size?: number;
  title?: string;
  description?: string;
}

/**
 * Prosty komponent wykresu kołowego
 * To jest tylko szkielet - w przyszłości zaimplementujemy pełną funkcjonalność z biblioteką Recharts
 */
export function PieChart({ data, size = 200, title, description }: PieChartProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0);

  // Domyślne kolory, jeśli nie są określone
  const defaultColors = [
    "bg-blue-500", "bg-green-500", "bg-yellow-500",
    "bg-red-500", "bg-purple-500", "bg-pink-500",
    "bg-indigo-500", "bg-teal-500", "bg-orange-500"
  ];

  return (
    <div className="w-full">
      {title && <h3 className="text-lg font-medium mb-2">{title}</h3>}
      {description && <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{description}</p>}

      <div className="flex flex-col md:flex-row items-center">
        <div style={{ width: `${size}px`, height: `${size}px` }} className="relative">
          <div className="absolute inset-0 rounded-full bg-gray-200 dark:bg-gray-700"></div>

          {/* Tutaj będzie właściwy wykres kołowy w przyszłości */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Wykres kołowy (wizualizacja)
            </span>
          </div>
        </div>

        <div className="mt-4 md:mt-0 md:ml-6 flex-1">
          <ul className="space-y-2">
            {data.map((item, index) => {
              const colorClass = item.color || defaultColors[index % defaultColors.length];
              const percentage = Math.round((item.value / total) * 100);

              return (
                <li key={index} className="flex items-center">
                  <div className={`w-4 h-4 rounded-full ${colorClass} mr-2`}></div>
                  <span className="flex-1">{item.label}</span>
                  <span className="font-medium">{percentage}%</span>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
}