

interface LineChartProps {
  data: {
    label: string;
    value: number;
  }[];
  height?: number;
  title?: string;
  description?: string;
}

/**
 * Prosty komponent wykresu liniowego
 * To jest tylko szkielet - w przyszłości zaimplementujemy pełną funkcjonalność z biblioteką Recharts
 */
export function LineChart({ data, height = 200, title, description }: LineChartProps) {
  const maxValue = Math.max(...data.map(item => item.value));
  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 100 - (item.value / maxValue) * 100;
    return `${x},${y}`;
  }).join(' ');

  return (
    <div className="w-full">
      {title && <h3 className="text-lg font-medium mb-2">{title}</h3>}
      {description && <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{description}</p>}

      <div className="w-full" style={{ height: `${height}px` }}>
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            points={points}
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-blue-500 dark:text-blue-400"
          />
        </svg>

        <div className="flex justify-between mt-2">
          {data.map((item, index) => (
            <div key={index} className="text-xs text-center">
              <div>{item.label}</div>
              <div className="font-medium">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}