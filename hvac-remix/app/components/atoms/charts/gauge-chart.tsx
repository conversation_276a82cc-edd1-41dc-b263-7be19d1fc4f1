

interface GaugeChartProps {
  value: number
  min?: number
  max?: number
  size?: number
  thickness?: number
  title?: string
  description?: string
  formatValue?: (value: number) => string
  colorScheme?: 'default' | 'health' | 'temperature' | 'custom'
  customColors?: {
    low: string
    medium: string
    high: string
  }
}

export function GaugeChart({
  value,
  min = 0,
  max = 100,
  size = 200,
  thickness = 20,
  title,
  description,
  formatValue = (value) => `${value}%`,
  colorScheme = 'default',
  customColors
}: GaugeChartProps) {
  // Normalize value between 0 and 1
  const normalizedValue = Math.min(Math.max((value - min) / (max - min), 0), 1)

  // Calculate angles for the arc
  const startAngle = -135
  const endAngle = 135
  const valueAngle = startAngle + normalizedValue * (endAngle - startAngle)

  // Calculate coordinates for the arc
  const radius = size / 2 - thickness / 2
  const center = size / 2

  // Calculate path for the background arc
  const backgroundArc = describeArc(center, center, radius, startAngle, endAngle, thickness)

  // Calculate path for the value arc
  const valueArc = describeArc(center, center, radius, startAngle, valueAngle, thickness)

  // Determine colors based on color scheme
  const getColors = () => {
    switch (colorScheme) {
      case 'health':
        return {
          low: 'rgb(239, 68, 68)', // red-500
          medium: 'rgb(245, 158, 11)', // amber-500
          high: 'rgb(34, 197, 94)' // green-500
        }
      case 'temperature':
        return {
          low: 'rgb(59, 130, 246)', // blue-500
          medium: 'rgb(245, 158, 11)', // amber-500
          high: 'rgb(239, 68, 68)' // red-500
        }
      case 'custom':
        return customColors || {
          low: 'rgb(239, 68, 68)',
          medium: 'rgb(245, 158, 11)',
          high: 'rgb(34, 197, 94)'
        }
      default:
        return {
          low: 'rgb(239, 68, 68)', // red-500
          medium: 'rgb(245, 158, 11)', // amber-500
          high: 'rgb(34, 197, 94)' // green-500
        }
    }
  }

  const colors = getColors()

  // Determine color for the value arc
  const getValueColor = () => {
    if (colorScheme === 'health' || colorScheme === 'default') {
      if (normalizedValue < 0.5) return colors.low
      if (normalizedValue < 0.8) return colors.medium
      return colors.high
    } else if (colorScheme === 'temperature') {
      if (normalizedValue < 0.3) return colors.low
      if (normalizedValue < 0.7) return colors.medium
      return colors.high
    } else {
      if (normalizedValue < 0.3) return colors.low
      if (normalizedValue < 0.7) return colors.medium
      return colors.high
    }
  }

  const valueColor = getValueColor()

  return (
    <div className="flex flex-col items-center">
      {title && <h3 className="text-lg font-medium mb-2">{title}</h3>}
      {description && <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">{description}</p>}

      <div className="relative" style={{ width: size, height: size }}>
        {/* Background arc */}
        <svg width={size} height={size} className="absolute top-0 left-0">
          <path
            d={backgroundArc}
            fill="none"
            stroke="currentColor"
            strokeWidth={thickness}
            className="text-gray-200 dark:text-gray-800"
          />
        </svg>

        {/* Value arc */}
        <svg width={size} height={size} className="absolute top-0 left-0">
          <path
            d={valueArc}
            fill="none"
            stroke={valueColor}
            strokeWidth={thickness}
            strokeLinecap="round"
          />
        </svg>

        {/* Value text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-3xl font-bold">{formatValue(value)}</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {min} - {max}
          </span>
        </div>
      </div>
    </div>
  )
}

// Helper function to describe an arc path
function describeArc(
  x: number,
  y: number,
  radius: number,
  startAngle: number,
  endAngle: number,
  thickness: number
) {
  const innerRadius = radius - thickness / 2

  const startAngleRad = (startAngle * Math.PI) / 180
  const endAngleRad = (endAngle * Math.PI) / 180

  const startX = x + innerRadius * Math.cos(startAngleRad)
  const startY = y + innerRadius * Math.sin(startAngleRad)
  const endX = x + innerRadius * Math.cos(endAngleRad)
  const endY = y + innerRadius * Math.sin(endAngleRad)

  const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1

  return [
    `M ${startX} ${startY}`,
    `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${endX} ${endY}`
  ].join(' ')
}
