import React from 'react';
import { useFetcher } from '@remix-run/react';
import { Skeleton } from "~/components/ui/skeleton";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { <PERSON><PERSON><PERSON> } from "~/components/atoms/charts/bar-chart";
import { Line<PERSON><PERSON> } from "~/components/atoms/charts/line-chart";
import { PieChart } from "~/components/atoms/charts/pie-chart";
import { GaugeChart } from "~/components/atoms/charts/gauge-chart";

interface DashboardWidgetProps {
  widgetId: string;
  size: 'small' | 'medium' | 'large';
}

export function DashboardWidget({ widgetId, size }: DashboardWidgetProps) {
  const fetcher = useFetcher();

  // Fetch widget data on mount
  React.useEffect(() => {
    if (fetcher.state === 'idle' && !fetcher.data) {
      fetcher.load(`/dashboard/widgets/${widgetId}`);
    }
  }, [widgetId, fetcher]);

  // Loading state
  if (fetcher.state === 'loading' || !fetcher.data) {
    return <WidgetSkeleton size={size} />;
  }

  // Error state
  if (fetcher.data && typeof fetcher.data === 'object' && 'error' in fetcher.data && fetcher.data.error) {
    return (
      <Alert variant="destructive" className="animate-fade-in border border-destructive/20 bg-destructive/5">
        <AlertCircle className="h-4 w-4 animate-pulse" />
        <AlertDescription className="font-medium">
          {String(fetcher.data.error)}
        </AlertDescription>
      </Alert>
    );
  }

  // Render widget based on type
  return <WidgetContent widgetId={widgetId} data={fetcher.data} size={size} />;
}

// Widget content renderer based on widget type
function WidgetContent({ widgetId, data, size }: { widgetId: string; data: any; size: string }) {
  const height = size === 'small' ? 150 : size === 'medium' ? 200 : 250;

  switch (widgetId) {
    case 'service-orders':
      return (
        <BarChart
          data={data.chartData}
          height={height}
          title={data.title}
          description={data.description}
        />
      );

    case 'device-types':
      return (
        <PieChart
          data={data.chartData}
          size={height}
          title={data.title}
          description={data.description}
        />
      );

    case 'revenue':
      return (
        <LineChart
          data={data.chartData}
          height={height}
          title={data.title}
          description={data.description}
        />
      );

    case 'technician-performance':
      return (
        <BarChart
          data={data.chartData}
          height={height}
          title={data.title}
          description={data.description}
        />
      );

    case 'customer-satisfaction':
    case 'system-health':
      return (
        <GaugeChart
          value={data.value}
          min={0}
          max={100}
          size={height}
          title={data.title}
          description={data.description}
          colorScheme={data.colorScheme || 'default'}
        />
      );

    case 'upcoming-service':
      return (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">{data.title}</h3>
          <div className="space-y-2">
            {data.items.map((item: any) => (
              <div key={item.id} className="flex justify-between items-center p-2 bg-muted rounded-md">
                <div>
                  <p className="font-medium">{item.title}</p>
                  <p className="text-xs text-muted-foreground">{item.subtitle}</p>
                </div>
                <div className="text-sm">{item.date}</div>
              </div>
            ))}
          </div>
        </div>
      );

    case 'my-schedule':
      return (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">{data.title}</h3>
          <div className="space-y-2">
            {data.events.map((event: any) => (
              <div key={event.id} className="flex justify-between items-center p-2 bg-muted rounded-md">
                <div>
                  <p className="font-medium">{event.title}</p>
                  <p className="text-xs text-muted-foreground">{event.location}</p>
                </div>
                <div className="text-sm">{event.time}</div>
              </div>
            ))}
          </div>
        </div>
      );

    case 'user-activity':
      return (
        <LineChart
          data={data.chartData}
          height={height}
          title={data.title}
          description={data.description}
        />
      );

    case 'my-performance':
      return (
        <div className="space-y-4">
          <h3 className="text-sm font-medium">{data.title}</h3>
          {data.metrics.map((metric: any) => (
            <div key={metric.name} className="space-y-1">
              <div className="flex justify-between">
                <span className="text-xs">{metric.name}</span>
                <span className="text-xs font-medium">{metric.value}{metric.unit}</span>
              </div>
              <div className="h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${(metric.value / metric.max) * 100}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      );

    default:
      return (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">Widget {widgetId} not implemented</p>
        </div>
      );
  }
}

// Skeleton loader for widgets
function WidgetSkeleton({ size }: { size: string }) {
  const height = size === 'small' ? 150 : size === 'medium' ? 200 : 250;

  return (
    <div className="space-y-3 animate-pulse">
      <div className="flex items-center space-x-2">
        <Skeleton className="h-3 w-3 rounded-full bg-primary/30" />
        <Skeleton className="h-5 w-1/2 bg-gradient-to-r from-primary/30 to-accent/30" />
      </div>
      <Skeleton className="h-4 w-3/4 bg-muted/60" />
      <div className="relative mt-4 overflow-hidden">
        <Skeleton className={`h-[${height}px] w-full rounded-md bg-gradient-to-r from-muted/40 via-muted/60 to-muted/40 animate-shimmer`} style={{ backgroundSize: '200% 100%' }} />
        <div className="absolute inset-0 flex items-center justify-center">
          <svg className="w-10 h-10 text-primary/20 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>
    </div>
  );
}
