import { useState, useEffect } from 'react';
import { getUnresolvedConflicts, resolveConflict } from '~/utils/offline-storage';
import { ConflictResolutionStrategy } from '~/utils/sync-manager';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import { Label } from '~/components/ui/label';
import { Separator } from '~/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { AlertCircle, Check, RefreshCw } from 'lucide-react';

interface ConflictResolutionProps {
  onResolved?: () => void;
}

/**
 * Component for resolving data conflicts
 */
export function ConflictResolution({ onResolved }: ConflictResolutionProps) {
  const [conflicts, setConflicts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [resolution, setResolution] = useState<ConflictResolutionStrategy>(
    ConflictResolutionStrategy.MANUAL
  );
  const [resolving, setResolving] = useState(false);
  const [mergedData, setMergedData] = useState<any>(null);

  // Load conflicts
  useEffect(() => {
    const loadConflicts = async () => {
      try {
        setLoading(true);
        const unresolvedConflicts = await getUnresolvedConflicts();
        setConflicts(unresolvedConflicts);

        if (unresolvedConflicts.length > 0) {
          // Initialize merged data for the first conflict
          setMergedData(createMergedData(unresolvedConflicts[0]));
        }
      } catch (error) {
        console.error('Error loading conflicts:', error);
      } finally {
        setLoading(false);
      }
    };

    loadConflicts();
  }, []);

  // Create merged data from conflict
  const createMergedData = (conflict: any) => {
    const merged = { ...conflict.serverData };

    // For each conflicting field, use local data
    for (const field of conflict.conflictFields) {
      merged[field] = conflict.localData[field];
    }

    return merged;
  };

  // Handle resolution
  const handleResolve = async () => {
    if (conflicts.length === 0 || currentIndex >= conflicts.length) {
      return;
    }

    const conflict = conflicts[currentIndex];

    try {
      setResolving(true);

      if (resolution === ConflictResolutionStrategy.MANUAL) {
        // Use merged data
        await resolveConflict(
          conflict.id,
          'merged',
          mergedData
        );
      } else {
        // Use strategy
        await resolveConflict(
          conflict.id,
          resolution === ConflictResolutionStrategy.USE_LOCAL ? 'local' : 'server'
        );
      }

      // Remove resolved conflict from list
      const updatedConflicts = [...conflicts];
      updatedConflicts.splice(currentIndex, 1);
      setConflicts(updatedConflicts);

      // If there are more conflicts, set up the next one
      if (updatedConflicts.length > 0) {
        const nextIndex = currentIndex >= updatedConflicts.length ? updatedConflicts.length - 1 : currentIndex;
        setCurrentIndex(nextIndex);
        setMergedData(createMergedData(updatedConflicts[nextIndex]));
      } else {
        // All conflicts resolved
        onResolved?.();
      }
    } catch (error) {
      console.error('Error resolving conflict:', error);
    } finally {
      setResolving(false);
    }
  };

  // Handle field selection for manual resolution
  const handleFieldSelection = (field: string, source: 'local' | 'server') => {
    if (!mergedData) return;

    const conflict = conflicts[currentIndex];
    const newValue = source === 'local' ? conflict.localData[field] : conflict.serverData[field];

    setMergedData({
      ...mergedData,
      [field]: newValue
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <RefreshCw className="animate-spin h-8 w-8 text-primary" />
        <span className="ml-2">Loading conflicts...</span>
      </div>
    );
  }

  if (conflicts.length === 0) {
    return (
      <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
        <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
        <AlertTitle>No conflicts to resolve</AlertTitle>
        <AlertDescription>
          All data is synchronized between your device and the server.
        </AlertDescription>
      </Alert>
    );
  }

  const conflict = conflicts[currentIndex];

  return (
    <div className="space-y-6">
      <Alert className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-900">
        <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertTitle>Data Conflict Detected</AlertTitle>
        <AlertDescription>
          Changes were made to the same data both locally and on the server.
          Please resolve the conflict by choosing which version to keep.
        </AlertDescription>
      </Alert>

      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Conflict {currentIndex + 1} of {conflicts.length}
        </h2>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
            disabled={currentIndex === 0}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => setCurrentIndex(Math.min(conflicts.length - 1, currentIndex + 1))}
            disabled={currentIndex === conflicts.length - 1}
          >
            Next
          </Button>
        </div>
      </div>

      <Card className="p-4">
        <h3 className="font-medium mb-2">
          {conflict.entityType}: {conflict.entityId}
        </h3>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Created: {new Date(conflict.createdAt).toLocaleString()}
        </p>

        <RadioGroup
          value={resolution}
          onValueChange={(value) => setResolution(value as ConflictResolutionStrategy)}
          className="space-y-2 mb-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value={ConflictResolutionStrategy.USE_LOCAL} id="use-local" />
            <Label htmlFor="use-local">Use local version (your changes)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value={ConflictResolutionStrategy.USE_SERVER} id="use-server" />
            <Label htmlFor="use-server">Use server version (discard your changes)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value={ConflictResolutionStrategy.MANUAL} id="manual" />
            <Label htmlFor="manual">Manually resolve field by field</Label>
          </div>
        </RadioGroup>

        {resolution === ConflictResolutionStrategy.MANUAL && (
          <Tabs defaultValue="fields" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="fields">Conflicting Fields</TabsTrigger>
              <TabsTrigger value="preview">Merged Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="fields" className="space-y-4">
              {conflict.conflictFields.map((field: string) => (
                <div key={field} className="border rounded-md p-4">
                  <h4 className="font-medium mb-2">{field}</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div
                      className={`p-3 rounded border cursor-pointer ${
                        mergedData && mergedData[field] === conflict.localData[field]
                          ? 'bg-blue-50 border-blue-300 dark:bg-blue-900/20 dark:border-blue-700'
                          : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                      }`}
                      onClick={() => handleFieldSelection(field, 'local')}
                    >
                      <div className="text-sm font-medium mb-1">Local Version</div>
                      <div className="break-words">
                        {typeof conflict.localData[field] === 'object'
                          ? JSON.stringify(conflict.localData[field])
                          : String(conflict.localData[field] || '')}
                      </div>
                    </div>

                    <div
                      className={`p-3 rounded border cursor-pointer ${
                        mergedData && mergedData[field] === conflict.serverData[field]
                          ? 'bg-blue-50 border-blue-300 dark:bg-blue-900/20 dark:border-blue-700'
                          : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                      }`}
                      onClick={() => handleFieldSelection(field, 'server')}
                    >
                      <div className="text-sm font-medium mb-1">Server Version</div>
                      <div className="break-words">
                        {typeof conflict.serverData[field] === 'object'
                          ? JSON.stringify(conflict.serverData[field])
                          : String(conflict.serverData[field] || '')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </TabsContent>

            <TabsContent value="preview">
              <div className="border rounded-md p-4 bg-gray-50 dark:bg-gray-800">
                <h4 className="font-medium mb-2">Merged Data Preview</h4>
                <pre className="text-sm overflow-auto p-2 bg-white dark:bg-gray-900 rounded border">
                  {JSON.stringify(mergedData, null, 2)}
                </pre>
              </div>
            </TabsContent>
          </Tabs>
        )}

        <Separator className="my-4" />

        <div className="flex justify-end">
          <Button
            onClick={handleResolve}
            disabled={resolving}
            className="min-w-[120px]"
          >
            {resolving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Resolving...
              </>
            ) : (
              'Resolve Conflict'
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
}
