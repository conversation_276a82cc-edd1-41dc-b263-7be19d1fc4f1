import { Link } from "@remix-run/react"
import { Card } from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { GaugeChart } from "~/components/atoms/charts/gauge-chart"
import type { Device } from "@prisma/client"

interface MaintenancePrediction {
  id: string
  deviceId: string
  createdAt: string
  failureProbability: number
  predictedComponent: string
  recommendedAction: string
  confidence: number
  predictedFailureDate?: string
}

interface PredictiveMaintenanceSummaryProps {
  device: Device & { 
    name: string
    model: string
    serialNumber: string
    manufacturer: string
    installationDate: string
    lastMaintenanceDate?: string
  }
  telemetryData?: Array<{
    id: string
    deviceId: string
    timestamp: string
    temperature?: number
    humidity?: number
    pressure?: number
    vibration?: number
    noise?: number
    powerUsage?: number
    runtime?: number
    cycles?: number
  }>
  predictions?: MaintenancePrediction[]
  maintenanceHistory?: Array<{
    id: string
    deviceId: string
    date: string
    type: string
    technician: string
    description: string
    components: string[]
  }>
  compact?: boolean
}

export function PredictiveMaintenanceSummary({
  device,
  telemetryData = [],
  predictions = [],
  compact = false
}: PredictiveMaintenanceSummaryProps) {
  // Get the latest prediction
  const latestPrediction = predictions.length > 0 
    ? predictions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
    : null
  
  // Get the latest telemetry data
  const latestTelemetry = telemetryData.length > 0
    ? telemetryData.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0]
    : null
  
  // Calculate health score based on prediction and telemetry
  const calculateHealthScore = () => {
    if (!latestPrediction) return 85 // Default good health if no prediction
    
    // Base score inversely related to failure probability
    const baseScore = 100 - (latestPrediction.failureProbability * 100)
    
    // Adjust based on telemetry if available
    if (latestTelemetry) {
      let telemetryFactor = 0
      
      // Check for abnormal temperature
      if (latestTelemetry.temperature && (latestTelemetry.temperature < 10 || latestTelemetry.temperature > 35)) {
        telemetryFactor -= 5
      }
      
      // Check for abnormal vibration
      if (latestTelemetry.vibration && latestTelemetry.vibration > 5) {
        telemetryFactor -= 10
      }
      
      // Check for abnormal noise
      if (latestTelemetry.noise && latestTelemetry.noise > 70) {
        telemetryFactor -= 5
      }
      
      return Math.max(0, Math.min(100, baseScore + telemetryFactor))
    }
    
    return baseScore
  }
  
  const healthScore = calculateHealthScore()
  
  // Format date or return placeholder
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Brak danych'
    return new Date(dateString).toLocaleDateString()
  }
  
  if (compact) {
    return (
      <Card className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium">Predykcja awarii</h3>
          {latestPrediction && (
            <Badge className={
              latestPrediction.failureProbability < 0.3 
                ? 'bg-green-500 text-white' 
                : latestPrediction.failureProbability < 0.7 
                  ? 'bg-yellow-500 text-white' 
                  : 'bg-red-500 text-white'
            }>
              {Math.round(latestPrediction.failureProbability * 100)}% ryzyka
            </Badge>
          )}
        </div>
        
        {latestPrediction ? (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">Komponent:</span>
              <span className="font-medium">{latestPrediction.predictedComponent}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">Zalecenie:</span>
              <span className="text-sm">{latestPrediction.recommendedAction}</span>
            </div>
            <Button 
              asChild
              variant="outline" 
              size="sm" 
              className="w-full mt-2"
            >
              <Link to={`/devices/${device.id}/predictions`}>
                Szczegóły
              </Link>
            </Button>
          </div>
        ) : (
          <div className="text-center py-2 text-gray-500 dark:text-gray-400">
            Brak predykcji dla tego urządzenia
          </div>
        )}
      </Card>
    )
  }
  
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Stan urządzenia</h2>
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          <div className="w-full md:w-1/3">
            <GaugeChart 
              value={healthScore} 
              title="Ogólny stan" 
              description="Bazując na predykcjach i telemetrii" 
              colorScheme="health"
            />
          </div>
          
          <div className="w-full md:w-2/3 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium">Informacje o urządzeniu</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Model:</span> {device.manufacturer} {device.model}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Nr seryjny:</span> {device.serialNumber}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Data instalacji:</span> {formatDate(device.installationDate)}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Ostatni serwis:</span> {formatDate(device.lastMaintenanceDate)}
                </p>
              </div>
              
              {latestPrediction && (
                <div className="space-y-2">
                  <h3 className="font-medium">Predykcja awarii</h3>
                  <div className="flex items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Ryzyko awarii:</span>
                    <Badge className={
                      latestPrediction.failureProbability < 0.3 
                        ? 'bg-green-500 text-white' 
                        : latestPrediction.failureProbability < 0.7 
                          ? 'bg-yellow-500 text-white' 
                          : 'bg-red-500 text-white'
                    }>
                      {Math.round(latestPrediction.failureProbability * 100)}%
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">Zagrożony komponent:</span> {latestPrediction.predictedComponent}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">Zalecane działanie:</span> {latestPrediction.recommendedAction}
                  </p>
                  {latestPrediction.predictedFailureDate && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Przewidywana data awarii:</span> {formatDate(latestPrediction.predictedFailureDate)}
                    </p>
                  )}
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">Pewność predykcji:</span> {Math.round(latestPrediction.confidence * 100)}%
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex space-x-3">
              <Button 
                asChild
                variant="outline"
              >
                <Link to={`/devices/${device.id}/telemetry`}>
                  Dane telemetryczne
                </Link>
              </Button>
              <Button 
                asChild
                variant="default"
              >
                <Link to={`/service-orders/new?deviceId=${device.id}&type=MAINTENANCE`}>
                  Zaplanuj serwis
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </Card>
      
      {latestTelemetry && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Ostatnie odczyty telemetryczne</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {latestTelemetry.temperature !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Temperatura</h3>
                <p className="text-xl font-semibold">{latestTelemetry.temperature}°C</p>
              </div>
            )}
            {latestTelemetry.humidity !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Wilgotność</h3>
                <p className="text-xl font-semibold">{latestTelemetry.humidity}%</p>
              </div>
            )}
            {latestTelemetry.pressure !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Ciśnienie</h3>
                <p className="text-xl font-semibold">{latestTelemetry.pressure} hPa</p>
              </div>
            )}
            {latestTelemetry.vibration !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Wibracje</h3>
                <p className="text-xl font-semibold">{latestTelemetry.vibration} mm/s</p>
              </div>
            )}
            {latestTelemetry.noise !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Hałas</h3>
                <p className="text-xl font-semibold">{latestTelemetry.noise} dB</p>
              </div>
            )}
            {latestTelemetry.powerUsage !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Zużycie energii</h3>
                <p className="text-xl font-semibold">{latestTelemetry.powerUsage} kWh</p>
              </div>
            )}
            {latestTelemetry.runtime !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Czas pracy</h3>
                <p className="text-xl font-semibold">{latestTelemetry.runtime} h</p>
              </div>
            )}
            {latestTelemetry.cycles !== undefined && (
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                <h3 className="text-sm text-gray-500 dark:text-gray-400">Cykle</h3>
                <p className="text-xl font-semibold">{latestTelemetry.cycles}</p>
              </div>
            )}
          </div>
          <div className="mt-4">
            <Button 
              asChild
              variant="outline"
            >
              <Link to={`/devices/${device.id}/telemetry`}>
                Zobacz wszystkie dane
              </Link>
            </Button>
          </div>
        </Card>
      )}
    </div>
  )
}
