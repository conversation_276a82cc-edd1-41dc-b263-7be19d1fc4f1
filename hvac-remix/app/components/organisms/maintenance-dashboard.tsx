import React from "react";
import React from "react";
import { Link, useFetcher } from "@remix-run/react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import {
  Calendar,
  AlertCircle,
  Wrench,
  Clock,
  CheckCircle,
  AlertTriangle,
  Loader2,
  BarChart2,
  Zap,
  BellRing,
  Gauge,
  Thermometer,
  Droplets,
  Settings
} from "lucide-react";
import { Skeleton } from "~/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

// Types for predictive maintenance data
interface PredictiveMaintenanceData {
  equipmentId: string;
  equipmentName: string;
  failureProbability: number;
  predictedFailureDate: string | null;
  recommendedActions: string[];
  sensorReadings: {
    temperature?: number;
    vibration?: number;
    pressure?: number;
    [key: string]: number | undefined;
  };
  lastUpdated: string;
}

// Custom hook for media queries (currently unused but kept for future use)
// const useMediaQuery = (query: string): boolean => {
//   const [matches, setMatches] = React.useState(false);

//   React.useEffect(() => {
//     const media = window.matchMedia(query);
//     if (media.matches !== matches) {
//       setMatches(media.matches);
//     }
//     const listener = () => setMatches(media.matches);
//     media.addListener(listener);
//     return () => media.removeListener(listener);
//   }, [matches, query]);

//   return matches;
// };

// Skeleton component for loading state
const DashboardSkeleton = () => (
  <div className="space-y-4">
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[...Array(4)].map((_, i) => (
        <Skeleton key={i} className="h-32 w-full" />
      ))}
    </div>
    <div className="grid gap-4 md:grid-cols-2">
      <Skeleton className="h-80 w-full" />
      <Skeleton className="h-80 w-full" />
    </div>
  </div>
);

interface EquipmentItem {
  id: string;
  name: string;
  model: string;
  status: 'OPERATIONAL' | 'MAINTENANCE_NEEDED' | 'WARNING' | 'CRITICAL' | 'OFFLINE';
  lastMaintenance?: string | null;
  nextMaintenance?: string | null;
  healthScore: number;
  predictedFailureDate?: string | null;
  recommendedActions?: string[];
  location?: string;
  serialNumber?: string;
  installationDate?: string | null;
}

interface MaintenanceRecordItem {
  id: string;
  equipmentId: string;
  equipmentName: string;
  type: string;
  description: string;
  completedAt: string;
  status: 'COMPLETED' | 'PENDING' | 'IN_PROGRESS';
  technician?: string;
  cost?: number;
}

interface UpcomingMaintenanceItem {
  id: string;
  equipmentId: string;
  equipmentName: string;
  type: string;
  scheduledDate: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimatedDuration?: string;
  requiredParts?: string[];
}

interface AlertItem {
  id: string;
  equipmentId: string;
  equipmentName: string;
  message: string;
  severity: 'WARNING' | 'ERROR' | 'CRITICAL';
  timestamp: string;
  acknowledged?: boolean;
  relatedMaintenanceId?: string;
}

interface MaintenanceDashboardProps {
  equipmentList?: EquipmentItem[];
  recentMaintenance?: MaintenanceRecordItem[];
  upcomingMaintenance?: UpcomingMaintenanceItem[];
  criticalAlerts?: AlertItem[];
  isLoading?: boolean;
  error?: string | null;
  onAcknowledgeAlert?: (alertId: string) => void;
  onScheduleMaintenance?: (equipmentId: string) => void;
  onViewDetails?: (equipmentId: string) => void;
}

// Component for sensor reading display
const SensorReading = ({ icon: Icon, value, unit, label }: {
  icon: React.ComponentType<{ className?: string }>,
  value: number | string | undefined,
  unit: string,
  label: string
}) => (
  <div className="flex items-center space-x-2">
    <div className="p-2 rounded-lg bg-muted">
      <Icon className="h-5 w-5 text-muted-foreground" />
    </div>
    <div>
      <p className="text-sm font-medium">
        {value !== undefined ? `${value} ${unit}` : '--'}
      </p>
      <p className="text-xs text-muted-foreground">{label}</p>
    </div>
  </div>
);

// Component for failure probability indicator
const FailureProbabilityIndicator = ({ probability }: { probability: number }) => {
  const getStatus = () => {
    if (probability >= 70) return 'high';
    if (probability >= 30) return 'medium';
    return 'low';
  };

  const status = getStatus();
  const statusColors = {
    high: 'bg-red-500',
    medium: 'bg-yellow-500',
    low: 'bg-green-500'
  };

  return (
    <div className="w-full bg-gray-200 rounded-full h-2.5">
      <div
        className={`h-2.5 rounded-full ${statusColors[status]}`}
        style={{ width: `${Math.min(probability, 100)}%` }}
      />
    </div>
  );
};

export function MaintenanceDashboard({
  equipmentList = [],

  upcomingMaintenance = [],
  criticalAlerts = [],
  isLoading = false,
  error = null,
  onAcknowledgeAlert,
  onScheduleMaintenance,
  onViewDetails
}: MaintenanceDashboardProps) {
  const [selectedEquipment, setSelectedEquipment] = React.useState<EquipmentItem | null>(null);
  const [activeTab, setActiveTab] = React.useState('overview');
  const fetcher = useFetcher<PredictiveMaintenanceData>();

  // Auto-select first equipment if none selected
  React.useEffect(() => {
    if (equipmentList.length > 0 && !selectedEquipment) {
      setSelectedEquipment(equipmentList[0]);
    }
  }, [equipmentList]);

  // Fetch predictive maintenance data when equipment is selected
  React.useEffect(() => {
    if (selectedEquipment) {
      fetcher.load(`/api/predictive-maintenance?equipmentId=${selectedEquipment.id}`);
    }
  }, [selectedEquipment]);

  // Handle loading and error states
  if (isLoading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Błąd</AlertTitle>
          <AlertDescription>
            Wystąpił błąd podczas ładowania danych: {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Get predictive maintenance data if available
  const predictiveData = fetcher.data;
  const isLoadingPredictive = fetcher.state === 'loading';


  // Calculate maintenance metrics with memoization
  const metrics = React.useMemo(() => {
    const critical = equipmentList.filter(eq => eq.status === 'CRITICAL').length;
    const warning = equipmentList.filter(eq => eq.status === 'WARNING').length;
    const operational = equipmentList.filter(eq => eq.status === 'OPERATIONAL').length;
    const overdue = equipmentList.filter(
      eq => eq.nextMaintenance && new Date(eq.nextMaintenance) < new Date()
    ).length;
    const health = equipmentList.length > 0
      ? equipmentList.reduce((sum, eq) => sum + eq.healthScore, 0) / equipmentList.length
      : 100;

    return { critical, warning, operational, overdue, health };
  }, [equipmentList]);

  const { critical: criticalEquipment, warning: warningEquipment,
          operational: operationalEquipment, overdue: overdueMaintenance,
          health: overallHealth } = metrics;

  // Get status badge with tooltip
  const getStatusBadge = (status: string, tooltip?: string) => {
    const badge = (() => {
      switch (status) {
        case 'OPERATIONAL':
          return <Badge variant="default" className="gap-1 bg-green-100 text-green-800 hover:bg-green-200">
            <CheckCircle className="h-3 w-3" /> Sprawne
          </Badge>;
        case 'WARNING':
          return <Badge variant="default" className="gap-1 bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            <AlertTriangle className="h-3 w-3" /> Ostrzeżenie
          </Badge>;
        case 'CRITICAL':
          return <Badge variant="destructive" className="gap-1">
            <AlertCircle className="h-3 w-3" /> Krytyczny
          </Badge>;
        case 'MAINTENANCE_NEEDED':
          return <Badge variant="default" className="gap-1">
            <Wrench className="h-3 w-3" /> Wymaga serwisu
          </Badge>;
        default:
          return <Badge variant="outline">Nieznany</Badge>;
      }
    })();

    if (!tooltip) return badge;

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{badge}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    );
  };

  // Get priority badge with tooltip
  const getPriorityBadge = (priority: string, tooltip?: string) => {
    const badge = (() => {
      switch (priority) {
        case 'LOW':
          return <Badge variant="outline">Niski</Badge>;
        case 'MEDIUM':
          return <Badge variant="default">Średni</Badge>;
        case 'HIGH':
          return <Badge variant="default" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Wysoki</Badge>;
        case 'CRITICAL':
          return <Badge variant="destructive">Krytyczny</Badge>;
        default:
          return <Badge variant="outline">Nieznany</Badge>;
      }
    })();

    if (!tooltip) return badge;

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{badge}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    );
  };

  // Format date with relative time
  const formatDate = (dateString?: string | null, showRelative = true) => {
    if (!dateString) return 'Brak danych';

    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Format date
    const formattedDate = date.toLocaleDateString('pl-PL', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    if (!showRelative) return formattedDate;

    // Add relative time
    if (diffInDays < 0) {
      const daysAgo = Math.abs(diffInDays);
      return `${formattedDate} (${daysAgo} ${daysAgo === 1 ? 'dzień' : 'dni'} temu)`;
    } else if (diffInDays === 0) {
      return `${formattedDate} (dzisiaj)`;
    } else {
      return `${formattedDate} (za ${diffInDays} ${diffInDays === 1 ? 'dzień' : 'dni'})`;
    }
  };
  // Format date with time
  const formatDateTime = (dateString?: string | null) => {
    if (!dateString) return 'Brak danych';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Nieprawidłowa data';

    return date.toLocaleDateString('pl-PL', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format date only
  const formatDateOnly = (dateString?: string | null) => {
    if (!dateString) return 'Brak danych';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Nieprawidłowa data';

    return date.toLocaleDateString('pl-PL', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Calculate metrics for the overview cards
  const metricsData = [
    {
      title: 'Sprawne urządzenia',
      value: operationalEquipment,
      total: equipmentList.length,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: 'positive' as const,
      trendValue: '5%',
      description: 'W porównaniu do zeszłego miesiąca'
    },
    {
      title: 'Wymagające uwagi',
      value: warningEquipment,
      total: equipmentList.length,
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      trend: 'neutral' as const,
      trendValue: '2%',
      description: 'W porównaniu do zeszłego miesiąca'
    },
    {
      title: 'Krytyczne',
      value: criticalEquipment,
      total: equipmentList.length,
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      trend: 'negative' as const,
      trendValue: '3%',
      description: 'Wymagają pilnej interwencji'
    },
    {
      title: 'Przeglądy',
      value: equipmentList.length - overdueMaintenance,
      total: equipmentList.length,
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: 'positive' as const,
      trendValue: '8%',
      description: 'Zaległe przeglądy'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header with title and actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Panel konserwacji</h1>
          <p className="text-muted-foreground">
            Monitoruj stan techniczny i planuj przeglądy
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Ustawienia
          </Button>
          <Button size="sm" onClick={() => onScheduleMaintenance?.('new')}>
            <Wrench className="mr-2 h-4 w-4" />
            Zaplanuj przegląd
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs
        defaultValue="overview"
        className="space-y-4"
        onValueChange={setActiveTab}
      >
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart2 className="mr-2 h-4 w-4" />
            Przegląd
          </TabsTrigger>
          <TabsTrigger value="predictive">
            <Zap className="mr-2 h-4 w-4" />
            Konserwacja predykcyjna
          </TabsTrigger>
          <TabsTrigger value="alerts">
            <BellRing className="mr-2 h-4 w-4" />
            Alerty
            {criticalAlerts.length > 0 && (
              <span className="ml-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                {criticalAlerts.length}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Metrics Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {metricsData.map((metric, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {metric.title}
                  </CardTitle>
                  <div className={`rounded-md p-2 ${metric.bgColor}`}>
                    <metric.icon className={`h-4 w-4 ${metric.color}`} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {metric.value} <span className="text-sm text-muted-foreground">/ {metric.total}</span>
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground mt-1">
                    {metric.trend === 'positive' ? (
                      <span className="text-green-600 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <polyline points="18 15 12 9 6 15"></polyline>
                        </svg>
                        {metric.trendValue}
                      </span>
                    ) : metric.trend === 'negative' ? (
                      <span className="text-red-600 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                        {metric.trendValue}
                      </span>
                    ) : (
                      <span className="text-yellow-600">
                        {metric.trendValue}
                      </span>
                    )}
                    <span className="ml-1">{metric.description}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

      {/* Health Score Card */}
      <Card>
        <CardHeader>
          <CardTitle>Stan techniczny</CardTitle>
          <CardDescription>
            Średni stan techniczny wszystkich urządzeń
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="text-3xl font-bold">{Math.round(overallHealth)}%</div>
              <div className="text-sm text-muted-foreground">
                {overallHealth > 80 ? 'Doskonały stan' :
                 overallHealth > 60 ? 'Dobry stan' :
                 overallHealth > 40 ? 'Wymaga uwagi' : 'Zły stan'}
              </div>
            </div>
            <div className="relative h-32 w-32">
              <svg
                className="h-full w-full"
                width="36"
                height="36"
                viewBox="0 0 36 36"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  className="stroke-current text-gray-200 dark:text-gray-800"
                  strokeWidth="3"
                ></circle>
                <g className="origin-center -rotate-90 transform">
                  <circle
                    cx="18"
                    cy="18"
                    r="16"
                    fill="none"
                    className={`stroke-current ${
                      overallHealth > 60 ? 'text-green-500' :
                      overallHealth > 30 ? 'text-yellow-500' : 'text-red-500'
                    }`}
                    strokeWidth="3"
                    strokeDasharray="100"
                    strokeDashoffset={100 - overallHealth}
                    strokeLinecap="round"
                  ></circle>
                </g>
              </svg>
              <div className="absolute inset-0 flex items-center justify-center text-sm text-muted-foreground">
                <span className="text-lg font-medium">
                  {Math.round(overallHealth)}%
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Equipment Health */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Stan urządzeń</CardTitle>
            <CardDescription>Podsumowanie stanu technicznego urządzeń</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {equipmentList.map((equipment) => (
              <div key={equipment.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Link to={`/devices/${equipment.id}`} className="font-medium hover:underline">
                      {equipment.name}
                    </Link>
                    {getStatusBadge(equipment.status)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {equipment.healthScore}%
                  </div>
                </div>
                <Progress value={equipment.healthScore} className="h-2" />
                {equipment.recommendedActions && equipment.recommendedActions.length > 0 && (
                  <div className="text-xs text-muted-foreground mt-1">
                    <span className="font-medium">Zalecenia:</span>{' '}
                    {equipment.recommendedActions.join(', ')}
                  </div>
                )}
              </div>
            ))}
            {equipmentList.length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                Brak urządzeń do wyświetlenia
              </div>
            )}
          </CardContent>
        </Card>

        {/* Right sidebar */}
        <div className="col-span-3 space-y-4">
          {/* Critical Alerts */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
                Krytyczne alerty
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {criticalAlerts.length > 0 ? (
                criticalAlerts.map((alert) => (
                  <Alert key={alert.id} variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <div className="ml-2">
                      <div className="font-medium">{alert.equipmentName}</div>
                      <div className="text-sm">{alert.message}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {formatDate(alert.timestamp)}
                      </div>
                    </div>
                  </Alert>
                ))
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Brak krytycznych alertów
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Maintenance */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5 text-blue-500" />
                Nadchodzące przeglądy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingMaintenance.length > 0 ? (
                <div className="space-y-4">
                  {upcomingMaintenance.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-start">
                      <div className="mr-3 mt-1">
                        <Wrench className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <Link
                            to={`/devices/${item.equipmentId}`}
                            className="font-medium hover:underline"
                          >
                            {item.equipmentName}
                          </Link>
                          {getPriorityBadge(item.priority)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {item.type}
                        </div>
                        <div className="text-xs text-muted-foreground flex items-center mt-1">
                          <Clock className="mr-1 h-3 w-3" />
                          {formatDate(item.scheduledDate)}
                        </div>
                      </div>
                    </div>
                  ))}
                  {upcomingMaintenance.length > 3 && (
                    <Button variant="ghost" className="w-full text-sm">
                      Zobacz wszystkie ({upcomingMaintenance.length})
                    </Button>
                  )}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Brak nadchodzących przeglądów
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
        </TabsContent>

        {/* Predictive Maintenance Tab */}
        <TabsContent value="predictive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Konserwacja predykcyjna</CardTitle>
              <CardDescription>
                Analiza predykcyjna stanu urządzeń i prognozowane awarie
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedEquipment ? (
                <div className="space-y-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-medium">{selectedEquipment.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {selectedEquipment.model} • {selectedEquipment.serialNumber || 'Brak numeru seryjnego'}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedEquipment(null)}
                    >
                      Zmień urządzenie
                    </Button>
                  </div>

                  {isLoadingPredictive ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : predictiveData ? (
                    <div className="space-y-6">
                      <div className="grid gap-4 md:grid-cols-3">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Ryzyko awarii</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-3xl font-bold">
                              {predictiveData.failureProbability}%
                            </div>
                            <FailureProbabilityIndicator probability={predictiveData.failureProbability} />
                            <p className="text-xs text-muted-foreground mt-2">
                              {predictiveData.failureProbability > 70 ? 'Wysokie ryzyko' :
                               predictiveData.failureProbability > 30 ? 'Średnie ryzyko' : 'Niskie ryzyko'}
                            </p>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Przewidywana data awarii</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-3xl font-bold">
                              {predictiveData.predictedFailureDate ?
                                formatDateOnly(predictiveData.predictedFailureDate) : 'Brak danych'}
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">
                              {predictiveData.predictedFailureDate ?
                                `Oszacowano: ${formatDate(predictiveData.lastUpdated, false)}` :
                                'Brak dostępnych prognoz'}
                            </p>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Zalecane działania</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              {predictiveData.recommendedActions.length > 0 ? (
                                predictiveData.recommendedActions.slice(0, 2).map((action, i) => (
                                  <li key={i} className="flex items-center">
                                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                    <span>{action}</span>
                                  </li>
                                ))
                              ) : (
                                <li className="text-muted-foreground">Brak zaleceń</li>
                              )}
                            </ul>
                            {predictiveData.recommendedActions.length > 2 && (
                              <Button variant="link" size="sm" className="h-auto p-0 mt-2">
                                Pokaż więcej
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      </div>

                      {/* Sensor Readings */}
                      <Card>
                        <CardHeader>
                          <CardTitle>Odczyty czujników</CardTitle>
                          <CardDescription>
                            Ostatnie odczyty z czujników urządzenia
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="grid gap-4 md:grid-cols-3">
                            <SensorReading
                              icon={Thermometer}
                              value={predictiveData.sensorReadings.temperature?.toFixed(1)}
                              unit="°C"
                              label="Temperatura"
                            />
                            <SensorReading
                              icon={Gauge}
                              value={predictiveData.sensorReadings.pressure?.toFixed(1)}
                              unit="bar"
                              label="Ciśnienie"
                            />
                            <SensorReading
                              icon={Droplets}
                              value={predictiveData.sensorReadings.vibration?.toFixed(1)}
                              unit="mm/s"
                              label="Wibracje"
                            />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Recommended Actions */}
                      {predictiveData.recommendedActions.length > 0 && (
                        <Card>
                          <CardHeader>
                            <CardTitle>Zalecane działania serwisowe</CardTitle>
                            <CardDescription>
                              Czynności zalecane na podstawie analizy stanu urządzenia
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-4">
                              {predictiveData.recommendedActions.map((action, i) => (
                                <li key={i} className="flex items-start">
                                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                                    <CheckCircle className="h-4 w-4 text-primary" />
                                  </div>
                                  <div>
                                    <p className="font-medium">Zalecenie {i + 1}</p>
                                    <p className="text-sm text-muted-foreground">{action}</p>
                                  </div>
                                </li>
                              ))}
                            </ul>
                            <div className="mt-6 flex justify-end">
                              <Button onClick={() => onScheduleMaintenance?.(selectedEquipment.id)}>
                                <Wrench className="mr-2 h-4 w-4" />
                                Zaplanuj serwis
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Zap className="h-10 w-10 mx-auto text-muted-foreground mb-2" />
                      <h3 className="text-lg font-medium">Brak danych predykcyjnych</h3>
                      <p className="text-muted-foreground text-sm mt-1">
                        Wybierz urządzenie, aby wyświetlić szczegóły
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Zap className="h-10 w-10 mx-auto text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">Wybierz urządzenie</h3>
                  <p className="text-muted-foreground text-sm mt-1">
                    Wybierz urządzenie z listy, aby zobaczyć szczegóły konserwacji predykcyjnej
                  </p>
                  <div className="mt-6">
                    <Button
                      variant="outline"
                      onClick={() => setSelectedEquipment(equipmentList[0])}
                      disabled={equipmentList.length === 0}
                    >
                      Pokaż pierwsze urządzenie
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alerty i powiadomienia</CardTitle>
              <CardDescription>
                {criticalAlerts.length > 0
                  ? `Masz ${criticalAlerts.length} nowych alertów`
                  : 'Brak nowych alertów'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {criticalAlerts.length > 0 ? (
                <div className="space-y-4">
                  {criticalAlerts.map((alert) => (
                    <Alert
                      key={alert.id}
                      variant={alert.severity === 'CRITICAL' ? 'destructive' : 'default'}
                      className="flex items-start"
                    >
                      <AlertCircle className="h-4 w-4 mt-1 mr-2" />
                      <div>
                        <div className="font-medium">
                          {alert.equipmentName} • {alert.severity === 'CRITICAL' ? 'Krytyczny' : 'Ostrzeżenie'}
                        </div>
                        <div className="text-sm mt-1">{alert.message}</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatDateTime(alert.timestamp)}
                        </div>
                        <div className="mt-2 flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onAcknowledgeAlert?.(alert.id)}
                          >
                            Oznacz jako przeczytane
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewDetails?.(alert.equipmentId)}
                          >
                            Zobacz szczegóły
                          </Button>
                        </div>
                      </div>
                    </Alert>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BellRing className="h-10 w-10 mx-auto text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">Brak aktywnych alertów</h3>
                  <p className="text-muted-foreground text-sm mt-1">
                    Wszystkie systemy działają prawidłowo
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
