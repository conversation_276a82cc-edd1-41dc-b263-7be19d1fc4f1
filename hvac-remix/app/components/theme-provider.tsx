import { createContext, useContext, useEffect, useState } from "react";

type Theme = "dark" | "light" | "system";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: "dark" | "light";
  isChangingTheme: boolean;
};

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
  resolvedTheme: "light",
  isChangingTheme: false,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "hvac-crm-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(
    () => (typeof window !== "undefined" && localStorage.getItem(storageKey) as Theme) || defaultTheme
  );
  const [resolvedTheme, setResolvedTheme] = useState<"dark" | "light">("light");
  const [isChangingTheme, setIsChangingTheme] = useState(false);

  // Watch for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleChange = () => {
      if (theme === "system") {
        const newResolvedTheme = mediaQuery.matches ? "dark" : "light";
        setResolvedTheme(newResolvedTheme);
        updateRootClass(newResolvedTheme);
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [theme]);

  // Function to update the root class with smooth transition
  const updateRootClass = (newTheme: "dark" | "light") => {
    const root = window.document.documentElement;

    // Add transition class
    root.classList.add("theme-transition");

    // Remove existing theme classes
    root.classList.remove("light", "dark");

    // Add new theme class
    root.classList.add(newTheme);

    // Remove transition class after animation completes
    setTimeout(() => {
      root.classList.remove("theme-transition");
    }, 300);
  };

  useEffect(() => {
    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";

      setResolvedTheme(systemTheme);
      updateRootClass(systemTheme);
    } else {
      setResolvedTheme(theme as "dark" | "light");
      updateRootClass(theme as "dark" | "light");
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setIsChangingTheme(true);
    localStorage.setItem(storageKey, newTheme);
    setThemeState(newTheme);

    // Reset the changing state after animation completes
    setTimeout(() => {
      setIsChangingTheme(false);
    }, 300);
  };

  const value = {
    theme,
    setTheme,
    resolvedTheme,
    isChangingTheme,
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};

// Helper function to get CSS variable values
export const getThemeColor = (colorVar: string): string => {
  if (typeof window === 'undefined') return '';

  const style = getComputedStyle(document.documentElement);
  return style.getPropertyValue(colorVar).trim();
};