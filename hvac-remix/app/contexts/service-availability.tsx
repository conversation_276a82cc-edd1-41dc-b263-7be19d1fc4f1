import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useFetcher } from '@remix-run/react';

// Define the service types
export enum ServiceType {
  BIELIK_LLM = 'bielik_llm',
  OCR = 'ocr',
  PREDICTIVE_MAINTENANCE = 'predictive_maintenance',
}

// Define the service status
export enum ServiceStatus {
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  UNKNOWN = 'unknown',
}

// Define the service info type
export interface ServiceInfo {
  status: ServiceStatus;
  lastChecked: Date | null;
  errorMessage?: string;
}

// Define the context type
export interface ServiceAvailabilityContextType {
  services: Record<ServiceType, ServiceInfo>;
  checkService: (service: ServiceType) => Promise<void>;
  checkAllServices: () => Promise<void>;
  isServiceAvailable: (service: ServiceType) => boolean;
}

// Create the context
const ServiceAvailabilityContext = createContext<ServiceAvailabilityContextType | null>(null);

// Initial state for services
const initialServicesState: Record<ServiceType, ServiceInfo> = {
  [ServiceType.BIELIK_LLM]: {
    status: ServiceStatus.UNKNOWN,
    lastChecked: null,
  },
  [ServiceType.OCR]: {
    status: ServiceStatus.UNKNOWN,
    lastChecked: null,
  },
  [ServiceType.PREDICTIVE_MAINTENANCE]: {
    status: ServiceStatus.UNKNOWN,
    lastChecked: null,
  },
};

// Provider component
export function ServiceAvailabilityProvider({ children }: { children: ReactNode }) {
  const [services, setServices] = useState<Record<ServiceType, ServiceInfo>>(initialServicesState);
  const fetcher = useFetcher();

  // Check a specific service
  const checkService = async (service: ServiceType) => {
    fetcher.load(`/api/service-status?service=${service}`);
  };

  // Check all services
  const checkAllServices = async () => {
    fetcher.load('/api/service-status');
  };

  // Determine if a service is available
  const isServiceAvailable = (service: ServiceType): boolean => {
    return services[service].status === ServiceStatus.AVAILABLE;
  };

  // Update services state when fetcher data changes
  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      const updatedServices = (fetcher.data as any)?.services;

      if (updatedServices) {
        setServices(prevServices => {
          const newServices = { ...prevServices };

          // Update each service with new data
          Object.keys(updatedServices).forEach(serviceKey => {
            const service = serviceKey as ServiceType;
            if (newServices[service]) {
              newServices[service] = {
                ...newServices[service],
                ...updatedServices[service],
                lastChecked: new Date(),
              };
            }
          });

          return newServices;
        });
      }
    }
  }, [fetcher.data, fetcher.state]);

  // Check services on initial load
  useEffect(() => {
    checkAllServices();

    // Set up periodic checking (every 5 minutes)
    const interval = setInterval(() => {
      checkAllServices();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <ServiceAvailabilityContext.Provider
      value={{
        services,
        checkService,
        checkAllServices,
        isServiceAvailable,
      }}
    >
      {children}
    </ServiceAvailabilityContext.Provider>
  );
}

// Custom hook to use the context
export function useServiceAvailability() {
  const context = useContext(ServiceAvailabilityContext);

  if (!context) {
    throw new Error('useServiceAvailability must be used within a ServiceAvailabilityProvider');
  }

  return context;
}
