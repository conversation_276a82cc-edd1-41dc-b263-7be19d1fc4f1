import { prisma } from "~/db.server";
import type { CalendarEntry } from "@prisma/client";
import { createServiceOrder } from "./service-order.server";
import { sendMultiChannelNotification } from "./notification.server";

/**
 * Interface for service order creation parameters
 */
export interface ServiceOrderCreationParams {
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  type?: string;
  scheduledDate?: Date;
  customerId: string;
  deviceId?: string;
  userId: string;
  calendarEntryId: string;
}

/**
 * Check if a calendar entry should trigger a service order creation
 */
export async function shouldCreateServiceOrder(calendarEntry: CalendarEntry): Promise<boolean> {
  // Don't create service orders for entries that already have one
  if (calendarEntry.serviceOrderId) {
    return false;
  }

  // Check if the entry has been semantically analyzed
  if (!calendarEntry.semanticAnalysis) {
    return false;
  }

  try {
    // Parse the semantic analysis
    const analysis = JSON.parse(calendarEntry.semanticAnalysis);

    // Check if the entry is a service-related event
    const serviceTypes = ['service', 'installation', 'inspection', 'repair', 'maintenance'];
    const isServiceEvent = 
      (calendarEntry.type && serviceTypes.includes(calendarEntry.type.toLowerCase())) ||
      (analysis.category && serviceTypes.includes(analysis.category.toLowerCase()));

    // Only create service orders for service-related events
    return isServiceEvent;
  } catch (error) {
    console.error("Error checking if calendar entry should create service order:", error);
    return false;
  }
}

/**
 * Find or create a customer based on calendar entry data
 */
export async function findOrCreateCustomer(
  calendarEntry: CalendarEntry,
  userId: string
): Promise<string | null> {
  try {
    // If no customer information is available, return null
    if (!calendarEntry.customer && !calendarEntry.clientContact) {
      return null;
    }

    let customerName = calendarEntry.customer;
    let clientContact: any = {};

    // Parse client contact information if available
    if (calendarEntry.clientContact) {
      try {
        clientContact = JSON.parse(calendarEntry.clientContact);
      } catch (e) {
        console.error("Error parsing client contact:", e);
      }
    }

    // If we have a customer name, try to find an existing customer
    if (customerName) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          userId,
          name: {
            contains: customerName,
            mode: 'insensitive'
          }
        }
      });

      if (existingCustomer) {
        return existingCustomer.id;
      }
    }

    // If no existing customer found, create a new one
    if (customerName) {
      const newCustomer = await prisma.customer.create({
        data: {
          name: customerName,
          email: clientContact.email || null,
          phone: clientContact.phone || null,
          address: clientContact.address || null,
          user: {
            connect: {
              id: userId
            }
          }
        }
      });

      return newCustomer.id;
    }

    return null;
  } catch (error) {
    console.error("Error finding or creating customer:", error);
    return null;
  }
}

/**
 * Find or create a device based on calendar entry data
 */
export async function findOrCreateDevice(
  calendarEntry: CalendarEntry,
  customerId: string,
  userId: string
): Promise<string | null> {
  try {
    // If no device information is available, return null
    if (!calendarEntry.device) {
      return null;
    }

    let deviceName = calendarEntry.device;
    let deviceDetails: any = {};

    // Parse semantic analysis for device details if available
    if (calendarEntry.semanticAnalysis) {
      try {
        const analysis = JSON.parse(calendarEntry.semanticAnalysis);
        if (analysis.entities && analysis.entities.device_details) {
          deviceDetails = analysis.entities.device_details;
        }
      } catch (e) {
        console.error("Error parsing semantic analysis for device details:", e);
      }
    }

    // Try to find an existing device
    const existingDevice = await prisma.device.findFirst({
      where: {
        userId,
        customerId,
        name: {
          contains: deviceName,
          mode: 'insensitive'
        }
      }
    });

    if (existingDevice) {
      return existingDevice.id;
    }

    // If no existing device found, create a new one
    const newDevice = await prisma.device.create({
      data: {
        name: deviceName,
        model: deviceDetails.model || null,
        manufacturer: deviceDetails.manufacturer || null,
        type: deviceDetails.type || null,
        user: {
          connect: {
            id: userId
          }
        },
        customer: {
          connect: {
            id: customerId
          }
        }
      }
    });

    return newDevice.id;
  } catch (error) {
    console.error("Error finding or creating device:", error);
    return null;
  }
}

/**
 * Create a service order from a calendar entry
 */
export async function createServiceOrderFromCalendarEntry(
  calendarEntry: CalendarEntry
): Promise<{
  success: boolean;
  message: string;
  serviceOrderId?: string;
}> {
  try {
    // Check if we should create a service order
    const shouldCreate = await shouldCreateServiceOrder(calendarEntry);
    if (!shouldCreate) {
      return {
        success: false,
        message: "Calendar entry does not qualify for service order creation"
      };
    }

    // Find or create customer
    const customerId = await findOrCreateCustomer(calendarEntry, calendarEntry.userId);
    if (!customerId) {
      return {
        success: false,
        message: "Could not determine customer for service order"
      };
    }

    // Find or create device
    const deviceId = await findOrCreateDevice(calendarEntry, customerId, calendarEntry.userId);

    // Determine service order type
    let serviceType = "SERVICE";
    if (calendarEntry.type) {
      if (calendarEntry.type.toLowerCase() === "installation") {
        serviceType = "INSTALLATION";
      } else if (calendarEntry.type.toLowerCase() === "inspection") {
        serviceType = "INSPECTION";
      }
    }

    // Determine priority
    let priority = "MEDIUM";
    if (calendarEntry.priority) {
      if (calendarEntry.priority.toLowerCase() === "high" || calendarEntry.priority.toLowerCase() === "critical") {
        priority = "HIGH";
      } else if (calendarEntry.priority.toLowerCase() === "low") {
        priority = "LOW";
      }
    }

    // Create service order
    const serviceOrderData = {
      title: calendarEntry.title,
      description: calendarEntry.description || "",
      status: "PENDING",
      priority,
      type: serviceType,
      scheduledDate: calendarEntry.startTime,
      customerId,
      deviceId: deviceId || undefined,
      userId: calendarEntry.userId
    };

    const serviceOrderResult = await createServiceOrder(serviceOrderData);

    if (!serviceOrderResult.success || !serviceOrderResult.data) {
      return {
        success: false,
        message: serviceOrderResult.message || "Failed to create service order"
      };
    }

    // Update calendar entry with service order ID
    await prisma.calendarEntry.update({
      where: { id: calendarEntry.id },
      data: {
        serviceOrderId: serviceOrderResult.data.id
      }
    });

    // Send notification about the new service order
    try {
      await sendMultiChannelNotification({
        type: 'SERVICE_ORDER_CREATED',
        title: 'New Service Order Created',
        message: `A new service order "${serviceOrderResult.data.title}" has been automatically created from a calendar event.`,
        userId: calendarEntry.userId,
        priority: 'medium',
        link: `/service-orders/${serviceOrderResult.data.id}`
      });
    } catch (notificationError) {
      console.error("Error sending notification:", notificationError);
      // Continue even if notification fails
    }

    return {
      success: true,
      message: "Successfully created service order from calendar entry",
      serviceOrderId: serviceOrderResult.data.id
    };
  } catch (error) {
    console.error("Error creating service order from calendar entry:", error);
    return {
      success: false,
      message: "Failed to create service order from calendar entry"
    };
  }
}

/**
 * Process all calendar entries that don't have service orders yet
 */
export async function processUnlinkedCalendarEntries(): Promise<{
  success: boolean;
  message: string;
  processed: number;
  created: number;
}> {
  try {
    // Find all calendar entries that have been semantically analyzed but don't have service orders
    const calendarEntries = await prisma.calendarEntry.findMany({
      where: {
        serviceOrderId: null,
        semanticAnalysis: {
          not: null
        }
      }
    });

    let created = 0;
    
    // Process each entry
    for (const entry of calendarEntries) {
      const result = await createServiceOrderFromCalendarEntry(entry);
      if (result.success) {
        created++;
      }
    }

    return {
      success: true,
      message: `Processed ${calendarEntries.length} calendar entries, created ${created} service orders`,
      processed: calendarEntries.length,
      created
    };
  } catch (error) {
    console.error("Error processing unlinked calendar entries:", error);
    return {
      success: false,
      message: "Failed to process unlinked calendar entries",
      processed: 0,
      created: 0
    };
  }
}