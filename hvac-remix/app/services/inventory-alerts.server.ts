import { prisma } from "~/db.server";
import { sendEmail } from "~/utils/email.server";
import { sendSMS } from "~/utils/sms.server";

export interface LowStockAlert {
  id: string;
  partId: string;
  partName: string;
  partNumber?: string;
  currentStock: number;
  minimumStock: number;
  reorderPoint: number;
  category?: string;
  supplier?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  location?: string;
  urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  daysUntilStockout?: number;
  averageDailyUsage?: number;
  suggestedOrderQuantity?: number;
}

export interface AlertConfiguration {
  userId: string;
  emailEnabled: boolean;
  smsEnabled: boolean;
  inAppEnabled: boolean;
  alertThresholds: {
    critical: number; // percentage below minimum stock
    high: number;
    medium: number;
    low: number;
  };
  notificationFrequency: 'IMMEDIATE' | 'DAILY' | 'WEEKLY';
  categories: string[]; // categories to monitor
}

/**
 * Get all parts that are below their reorder point
 */
export async function getLowStockParts(userId?: string): Promise<LowStockAlert[]> {
  const whereClause: any = {
    isActive: true,
    OR: [
      {
        currentStock: {
          lte: prisma.inventoryPart.fields.minimumStock
        }
      },
      {
        currentStock: {
          lte: prisma.inventoryPart.fields.reorderPoint
        }
      }
    ]
  };

  // If userId is provided, filter by user's parts
  if (userId) {
    // Assuming parts are associated with users through service orders or other means
    // For now, we'll get all parts but this should be filtered by user context
  }

  const lowStockParts = await prisma.inventoryPart.findMany({
    where: whereClause,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
        }
      },
      inventoryLocations: {
        include: {
          location: {
            select: {
              name: true,
            }
          }
        }
      }
    },
    orderBy: [
      {
        currentStock: 'asc'
      },
      {
        minimumStock: 'desc'
      }
    ]
  });

  // Calculate usage patterns and urgency levels
  const alerts: LowStockAlert[] = await Promise.all(
    lowStockParts.map(async (part) => {
      const urgencyLevel = calculateUrgencyLevel(part);
      const usageData = await calculateUsagePattern(part.id);
      
      return {
        id: part.id,
        partId: part.id,
        partName: part.name,
        partNumber: part.partNumber || undefined,
        currentStock: part.currentStock,
        minimumStock: part.minimumStock,
        reorderPoint: part.reorderPoint,
        category: part.category || undefined,
        supplier: part.supplier || undefined,
        location: part.inventoryLocations[0]?.location.name || undefined,
        urgencyLevel,
        daysUntilStockout: usageData.daysUntilStockout,
        averageDailyUsage: usageData.averageDailyUsage,
        suggestedOrderQuantity: calculateSuggestedOrderQuantity(part, usageData),
      };
    })
  );

  return alerts;
}

/**
 * Calculate urgency level based on stock levels and usage patterns
 */
function calculateUrgencyLevel(part: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const stockPercentage = (part.currentStock / part.minimumStock) * 100;
  
  if (part.currentStock === 0) {
    return 'CRITICAL';
  } else if (stockPercentage <= 25) {
    return 'HIGH';
  } else if (stockPercentage <= 50) {
    return 'MEDIUM';
  } else {
    return 'LOW';
  }
}

/**
 * Calculate usage patterns for a part
 */
async function calculateUsagePattern(partId: string): Promise<{
  averageDailyUsage: number;
  daysUntilStockout?: number;
}> {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Get usage transactions for the last 30 days
  const usageTransactions = await prisma.inventoryTransaction.findMany({
    where: {
      partId,
      type: 'OUTBOUND',
      createdAt: {
        gte: thirtyDaysAgo,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  if (usageTransactions.length === 0) {
    return {
      averageDailyUsage: 0,
    };
  }

  // Calculate total usage and average daily usage
  const totalUsage = usageTransactions.reduce((sum, transaction) => 
    sum + Math.abs(transaction.quantity), 0
  );
  
  const averageDailyUsage = totalUsage / 30;

  // Calculate days until stockout
  const part = await prisma.inventoryPart.findUnique({
    where: { id: partId },
    select: { currentStock: true },
  });

  const daysUntilStockout = averageDailyUsage > 0 
    ? Math.floor((part?.currentStock || 0) / averageDailyUsage)
    : undefined;

  return {
    averageDailyUsage,
    daysUntilStockout,
  };
}

/**
 * Calculate suggested order quantity based on usage patterns and lead times
 */
function calculateSuggestedOrderQuantity(part: any, usageData: any): number {
  const { averageDailyUsage } = usageData;
  
  // Default lead time of 7 days if not specified
  const leadTimeDays = part.leadTimeDays || 7;
  
  // Safety stock (30 days worth)
  const safetyStock = averageDailyUsage * 30;
  
  // Lead time demand
  const leadTimeDemand = averageDailyUsage * leadTimeDays;
  
  // Suggested order quantity: bring stock to maximum level
  const maxStock = part.maximumStock || (part.minimumStock * 3);
  const suggestedQuantity = Math.max(
    maxStock - part.currentStock,
    leadTimeDemand + safetyStock
  );

  return Math.ceil(suggestedQuantity);
}

/**
 * Send low stock alerts to users
 */
export async function sendLowStockAlerts(userId: string, alerts: LowStockAlert[]): Promise<void> {
  if (alerts.length === 0) return;

  // Get user notification preferences
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      email: true,
      settings: {
        select: {
          emailNotifications: true,
          smsNotifications: true,
          deviceAlerts: true,
        }
      }
    },
  });

  if (!user) return;

  // Group alerts by urgency
  const criticalAlerts = alerts.filter(a => a.urgencyLevel === 'CRITICAL');
  const highAlerts = alerts.filter(a => a.urgencyLevel === 'HIGH');
  const mediumAlerts = alerts.filter(a => a.urgencyLevel === 'MEDIUM');
  const lowAlerts = alerts.filter(a => a.urgencyLevel === 'LOW');

  // Send email notification
  if (user.email) {
    await sendLowStockEmail(user.email, {
      critical: criticalAlerts,
      high: highAlerts,
      medium: mediumAlerts,
      low: lowAlerts,
    });
  }

  // Create in-app notifications
  await createInAppNotifications(userId, alerts);
}

/**
 * Send low stock email notification
 */
async function sendLowStockEmail(email: string, alertsByUrgency: any): Promise<void> {
  const totalAlerts = Object.values(alertsByUrgency).flat().length;
  
  const subject = `Low Stock Alert - ${totalAlerts} items need attention`;
  
  let htmlContent = `
    <h2>Low Stock Alert</h2>
    <p>The following items are running low and may need to be reordered:</p>
  `;

  // Add critical alerts
  if (alertsByUrgency.critical.length > 0) {
    htmlContent += `
      <h3 style="color: #dc2626;">🚨 Critical (Out of Stock)</h3>
      <ul>
        ${alertsByUrgency.critical.map((alert: LowStockAlert) => `
          <li><strong>${alert.partName}</strong> - Current: ${alert.currentStock}, Minimum: ${alert.minimumStock}</li>
        `).join('')}
      </ul>
    `;
  }

  // Add high priority alerts
  if (alertsByUrgency.high.length > 0) {
    htmlContent += `
      <h3 style="color: #ea580c;">⚠️ High Priority</h3>
      <ul>
        ${alertsByUrgency.high.map((alert: LowStockAlert) => `
          <li><strong>${alert.partName}</strong> - Current: ${alert.currentStock}, Minimum: ${alert.minimumStock}
          ${alert.daysUntilStockout ? ` (${alert.daysUntilStockout} days until stockout)` : ''}</li>
        `).join('')}
      </ul>
    `;
  }

  // Add medium and low priority alerts (summarized)
  if (alertsByUrgency.medium.length > 0 || alertsByUrgency.low.length > 0) {
    htmlContent += `
      <h3 style="color: #ca8a04;">📋 Other Items to Monitor</h3>
      <p>${alertsByUrgency.medium.length + alertsByUrgency.low.length} additional items are approaching their reorder points.</p>
    `;
  }

  htmlContent += `
    <p><a href="${process.env.BASE_URL}/inventory?lowStock=true">View all low stock items</a></p>
  `;

  await sendEmail({
    to: email,
    subject,
    html: htmlContent,
  });
}

/**
 * Create in-app notifications for low stock alerts
 */
async function createInAppNotifications(userId: string, alerts: LowStockAlert[]): Promise<void> {
  const criticalAlerts = alerts.filter(a => a.urgencyLevel === 'CRITICAL');
  const highAlerts = alerts.filter(a => a.urgencyLevel === 'HIGH');

  // Create notifications for critical and high priority alerts
  const notificationsToCreate = [
    ...criticalAlerts.map(alert => ({
      userId,
      type: 'INVENTORY_CRITICAL',
      title: `Critical: ${alert.partName} is out of stock`,
      message: `${alert.partName} has 0 items in stock. Immediate action required.`,
      data: JSON.stringify({ partId: alert.partId, urgencyLevel: alert.urgencyLevel }),
      isRead: false,
    })),
    ...highAlerts.map(alert => ({
      userId,
      type: 'INVENTORY_LOW',
      title: `Low Stock: ${alert.partName}`,
      message: `${alert.partName} has only ${alert.currentStock} items left (minimum: ${alert.minimumStock}).`,
      data: JSON.stringify({ partId: alert.partId, urgencyLevel: alert.urgencyLevel }),
      isRead: false,
    }))
  ];

  if (notificationsToCreate.length > 0) {
    await prisma.notification.createMany({
      data: notificationsToCreate,
    });
  }
}

/**
 * Check and send automated alerts (to be called by a scheduled job)
 */
export async function checkAndSendAutomatedAlerts(): Promise<void> {
  // Get all users who have inventory alert preferences enabled
  const users = await prisma.user.findMany({
    where: {
      settings: {
        deviceAlerts: true,
      }
    },
    select: {
      id: true,
      email: true,
    },
  });

  for (const user of users) {
    try {
      const alerts = await getLowStockParts(user.id);
      if (alerts.length > 0) {
        await sendLowStockAlerts(user.id, alerts);
      }
    } catch (error) {
      console.error(`Failed to send alerts for user ${user.id}:`, error);
    }
  }
}
