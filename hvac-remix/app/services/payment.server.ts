import Stripe from 'stripe';
import { prisma } from '~/db.server';
import type { Customer, Payment } from '@prisma/client';

// Initialize Stripe with the secret key from environment variables if available
let stripe: Stripe | null = null;

// Only initialize Stripe if the API key is provided
if (process.env.STRIPE_SECRET_KEY) {
  try {
    stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16', // Use the latest API version
    });
  } catch (error) {
    console.warn('Failed to initialize Stripe:', error);
  }
}

/**
 * Create or retrieve a Stripe customer for a given customer
 * @param customer - The customer to create or retrieve a Stripe customer for
 * @returns The Stripe customer ID or null if Stripe is not initialized
 */
export async function getOrCreateStripeCustomer(customer: Customer): Promise<string | null> {
  // If Stripe is not initialized, return null
  if (!stripe) {
    console.warn('Stripe is not initialized. Cannot create or retrieve Stripe customer.');
    return null;
  }

  // If the customer already has a Stripe customer ID, return it
  if (customer.stripeCustomerId) {
    return customer.stripeCustomerId;
  }

  try {
    // Create a new Stripe customer
    const stripeCustomer = await stripe.customers.create({
      email: customer.email || undefined,
      name: customer.name,
      phone: customer.phone || undefined,
      address: customer.address ? {
        line1: customer.address,
        city: customer.city || undefined,
        postal_code: customer.postalCode || undefined,
        country: customer.country || 'PL',
      } : undefined,
      metadata: {
        customerId: customer.id,
      },
    });

    // Update the customer with the Stripe customer ID
    await prisma.customer.update({
      where: { id: customer.id },
      data: { stripeCustomerId: stripeCustomer.id },
    });

    return stripeCustomer.id;
  } catch (error) {
    console.error('Error creating Stripe customer:', error);
    return null;
  }
}

/**
 * Create a payment intent for an invoice
 * @param invoiceId - The ID of the invoice to create a payment intent for
 * @returns The payment intent client secret and ID, or null if Stripe is not initialized
 */
export async function createPaymentIntent(invoiceId: string): Promise<{ clientSecret: string, paymentIntentId: string } | null> {
  // If Stripe is not initialized, return null
  if (!stripe) {
    console.warn('Stripe is not initialized. Cannot create payment intent.');
    return null;
  }

  try {
    // Get the invoice with customer
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: { customer: true },
    });

    if (!invoice) {
      throw new Error(`Invoice with ID ${invoiceId} not found`);
    }

    if (!invoice.totalAmount) {
      throw new Error(`Invoice with ID ${invoiceId} has no total amount`);
    }

    // Get or create a Stripe customer
    const stripeCustomerId = await getOrCreateStripeCustomer(invoice.customer);

    if (!stripeCustomerId) {
      console.warn('Could not get or create Stripe customer. Cannot create payment intent.');
      return null;
    }

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(invoice.totalAmount * 100), // Convert to cents
      currency: 'pln',
      customer: stripeCustomerId,
      metadata: {
        invoiceId: invoice.id,
        invoiceNumber: invoice.invoiceNumber || invoice.id,
        customerId: invoice.customerId,
      },
      description: `Payment for invoice ${invoice.invoiceNumber || invoice.id}`,
    });

    return {
      clientSecret: paymentIntent.client_secret || '',
      paymentIntentId: paymentIntent.id,
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return null;
  }
}

/**
 * Record a payment for an invoice
 * @param data - The payment data
 * @returns The created payment
 */
export async function recordPayment(data: {
  invoiceId: string;
  amount: number;
  paymentMethod: string;
  paymentMethodId?: string;
  paymentIntentId?: string;
  transactionId?: string;
  status?: string;
  metadata?: Record<string, any>;
}): Promise<Payment> {
  const invoice = await prisma.invoice.findUnique({
    where: { id: data.invoiceId },
    include: { payments: true },
  });

  if (!invoice) {
    throw new Error(`Invoice with ID ${data.invoiceId} not found`);
  }

  // Create the payment
  const payment = await prisma.payment.create({
    data: {
      amount: data.amount,
      paymentMethod: data.paymentMethod,
      paymentMethodId: data.paymentMethodId,
      paymentIntentId: data.paymentIntentId,
      transactionId: data.transactionId,
      status: data.status || 'COMPLETED',
      metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
      invoiceId: data.invoiceId,
      customerId: invoice.customerId,
    },
  });

  // Calculate the total paid amount
  const totalPaid = invoice.payments.reduce(
    (sum, payment) => sum + payment.amount,
    0
  ) + data.amount;

  // Update the invoice payment status
  let paymentStatus = 'UNPAID';
  if (totalPaid >= (invoice.totalAmount || 0)) {
    paymentStatus = 'PAID';
  } else if (totalPaid > 0) {
    paymentStatus = 'PARTIALLY_PAID';
  }

  await prisma.invoice.update({
    where: { id: data.invoiceId },
    data: { paymentStatus },
  });

  return payment;
}

/**
 * Process a webhook event from Stripe
 * @param event - The Stripe webhook event
 * @returns The processed event result
 */
export async function processWebhookEvent(event: Stripe.Event): Promise<{ success: boolean; message: string }> {
  // If Stripe is not initialized, return an error
  if (!stripe) {
    console.warn('Stripe is not initialized. Cannot process webhook event.');
    return { success: false, message: 'Stripe is not initialized' };
  }

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        return await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
      case 'payment_intent.payment_failed':
        return await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
      case 'charge.refunded':
        return await handleChargeRefunded(event.data.object as Stripe.Charge);
      default:
        return { success: true, message: `Unhandled event type: ${event.type}` };
    }
  } catch (error) {
    console.error('Error processing webhook event:', error);
    return { success: false, message: `Error processing webhook event: ${error}` };
  }
}

/**
 * Handle a payment intent succeeded event
 * @param paymentIntent - The payment intent
 * @returns The result of handling the event
 */
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<{ success: boolean; message: string }> {
  const { invoiceId } = paymentIntent.metadata || {};

  if (!invoiceId) {
    return { success: false, message: 'No invoiceId found in payment intent metadata' };
  }

  // Record the payment
  await recordPayment({
    invoiceId,
    amount: paymentIntent.amount / 100, // Convert from cents
    paymentMethod: 'CREDIT_CARD',
    paymentMethodId: paymentIntent.payment_method as string,
    paymentIntentId: paymentIntent.id,
    status: 'COMPLETED',
    metadata: paymentIntent.metadata,
  });

  return { success: true, message: `Payment recorded for invoice ${invoiceId}` };
}

/**
 * Handle a payment intent failed event
 * @param paymentIntent - The payment intent
 * @returns The result of handling the event
 */
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<{ success: boolean; message: string }> {
  const { invoiceId } = paymentIntent.metadata || {};

  if (!invoiceId) {
    return { success: false, message: 'No invoiceId found in payment intent metadata' };
  }

  // Record the failed payment
  await recordPayment({
    invoiceId,
    amount: paymentIntent.amount / 100, // Convert from cents
    paymentMethod: 'CREDIT_CARD',
    paymentMethodId: paymentIntent.payment_method as string,
    paymentIntentId: paymentIntent.id,
    status: 'FAILED',
    metadata: {
      ...paymentIntent.metadata,
      error: paymentIntent.last_payment_error?.message,
    },
  });

  return { success: true, message: `Failed payment recorded for invoice ${invoiceId}` };
}

/**
 * Handle a charge refunded event
 * @param charge - The charge
 * @returns The result of handling the event
 */
async function handleChargeRefunded(charge: Stripe.Charge): Promise<{ success: boolean; message: string }> {
  const paymentIntentId = charge.payment_intent as string;

  if (!paymentIntentId) {
    return { success: false, message: 'No payment intent ID found in charge' };
  }

  // Find the payment with this payment intent ID
  const payment = await prisma.payment.findFirst({
    where: { paymentIntentId },
  });

  if (!payment) {
    return { success: false, message: `No payment found with payment intent ID ${paymentIntentId}` };
  }

  // Update the payment status
  await prisma.payment.update({
    where: { id: payment.id },
    data: {
      status: 'REFUNDED',
      refundId: charge.refunds?.data[0]?.id,
      refundAmount: charge.amount_refunded / 100, // Convert from cents
      refundedAt: new Date(),
    },
  });

  // Update the invoice payment status
  await prisma.invoice.update({
    where: { id: payment.invoiceId },
    data: { paymentStatus: 'REFUNDED' },
  });

  return { success: true, message: `Refund recorded for payment ${payment.id}` };
}

/**
 * Get payment methods for a customer
 * @param customerId - The customer ID
 * @returns The customer's payment methods
 */
export async function getCustomerPaymentMethods(customerId: string) {
  return prisma.paymentMethod.findMany({
    where: { customerId },
    orderBy: { createdAt: 'desc' },
  });
}

/**
 * Save a payment method for a customer
 * @param customerId - The customer ID
 * @param paymentMethodId - The Stripe payment method ID
 * @returns The saved payment method or null if Stripe is not initialized
 */
export async function savePaymentMethod(customerId: string, paymentMethodId: string) {
  // If Stripe is not initialized, return null
  if (!stripe) {
    console.warn('Stripe is not initialized. Cannot save payment method.');
    return null;
  }

  try {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
    });

    if (!customer) {
      throw new Error(`Customer with ID ${customerId} not found`);
    }

    // Get or create a Stripe customer
    const stripeCustomerId = await getOrCreateStripeCustomer(customer);

    if (!stripeCustomerId) {
      console.warn('Could not get or create Stripe customer. Cannot save payment method.');
      return null;
    }

    // Attach the payment method to the customer in Stripe
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: stripeCustomerId,
    });

    // Get the payment method details from Stripe
    const stripePaymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

    // Save the payment method in the database
    return prisma.paymentMethod.create({
      data: {
        type: stripePaymentMethod.type.toUpperCase(),
        name: `${stripePaymentMethod.card?.brand} ending in ${stripePaymentMethod.card?.last4}`,
        lastFourDigits: stripePaymentMethod.card?.last4,
        expiryMonth: stripePaymentMethod.card?.exp_month,
        expiryYear: stripePaymentMethod.card?.exp_year,
        brand: stripePaymentMethod.card?.brand,
        paymentMethodId: paymentMethodId,
        customerId,
      },
    });
  } catch (error) {
    console.error('Error saving payment method:', error);
    return null;
  }
}
