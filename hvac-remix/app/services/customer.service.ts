import { prisma } from "~/db.server";
import type { Customer as <PERSON>risma<PERSON>ust<PERSON>, <PERSON><PERSON>, ServiceOrder } from "@prisma/client";
import { indexCustomer, deleteEmbedding, COLLECTIONS } from "./qdrant.server";

export interface Customer extends PrismaCustomer {}

export interface CustomerWithRelations extends PrismaCustomer {
  devices: Device[];
  serviceOrders: (ServiceOrder & { device: Device | null })[];
}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  type?: string;
  recentActivity?: boolean;
  maintenanceDue?: boolean;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all customers with pagination, filtering, and sorting
 */
export async function getCustomers(
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<PaginatedResponse<Customer>> {
  try {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = pagination;

    const { search, status, type, recentActivity, maintenanceDue } = filters;

    // Build where clause
    const where: any = { userId };

    // Add search filter if provided
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add status filter if provided
    if (status) {
      where.status = status;
    }

    // Add type filter if provided
    if (type) {
      where.type = type;
    }

    // Add recent activity filter if provided
    if (recentActivity) {
      // Include customers with recent service orders or communications (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      where.OR = [
        ...(where.OR || []),
        {
          serviceOrders: {
            some: {
              createdAt: {
                gte: thirtyDaysAgo
              }
            }
          }
        },
        {
          communications: {
            some: {
              timestamp: {
                gte: thirtyDaysAgo
              }
            }
          }
        }
      ];
    }

    // Add maintenance due filter if provided
    if (maintenanceDue) {
      where.devices = {
        some: {
          nextMaintenanceDate: {
            lte: new Date(new Date().setDate(new Date().getDate() + 30)) // Next 30 days
          }
        }
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.customer.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get customers with pagination
    const customers = await prisma.customer.findMany({
      where,
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        devices: {
          select: {
            id: true,
            name: true,
          },
        },
        serviceOrders: {
          select: {
            id: true,
            title: true,
            status: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });

    return {
      data: customers,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch customers:', error);
    return {
      data: null,
      error: 'Failed to fetch customers',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}

/**
 * Get a customer by ID
 */
export async function getCustomerById(id: string, userId: string): Promise<ServiceResponse<CustomerWithRelations>> {
  try {
    const customer = await prisma.customer.findUnique({
      where: { id, userId },
      include: {
        devices: true,
        serviceOrders: {
          orderBy: { createdAt: 'desc' },
          include: {
            device: true,
          },
        },
      },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found',
        success: false
      };
    }

    return {
      data: customer,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch customer:', error);
    return {
      data: null,
      error: 'Failed to fetch customer',
      success: false
    };
  }
}

/**
 * Create a new customer
 */
export async function createCustomer(
  data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<ServiceResponse<Customer>> {
  try {
    // Add userId to data
    const customerData = {
      ...data,
      userId,
    };

    // Create customer in database
    const customer = await prisma.customer.create({
      data: customerData,
    });

    // Index customer in Supabase for semantic search
    try {
      await indexCustomer(prisma, customer.id);
    } catch (indexError) {
      console.error('Failed to index customer in Supabase:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: customer,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create customer:', error);
    return {
      data: null,
      error: 'Failed to create customer',
      success: false
    };
  }
}

/**
 * Update an existing customer
 */
export async function updateCustomer(
  id: string,
  data: Partial<Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>>,
  userId: string
): Promise<ServiceResponse<Customer>> {
  try {
    // Check if customer exists and belongs to user
    const existingCustomer = await prisma.customer.findUnique({
      where: { id, userId },
    });

    if (!existingCustomer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Update customer
    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data,
    });

    // Re-index customer in Supabase
    try {
      await indexCustomer(prisma, id);
    } catch (indexError) {
      console.error('Failed to re-index customer in Supabase:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: updatedCustomer,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to update customer:', error);
    return {
      data: null,
      error: 'Failed to update customer',
      success: false
    };
  }
}

/**
 * Delete a customer
 */
export async function deleteCustomer(id: string, userId: string): Promise<ServiceResponse<boolean>> {
  try {
    // Check if customer exists and belongs to user
    const existingCustomer = await prisma.customer.findUnique({
      where: { id, userId },
    });

    if (!existingCustomer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Delete customer from Supabase vector storage first
    try {
      await deleteEmbedding(COLLECTIONS.CUSTOMERS, id);
    } catch (deleteError) {
      console.error('Failed to delete customer from Supabase vector storage:', deleteError);
      // Don't fail the operation if vector deletion fails
    }

    // Delete customer from database
    await prisma.customer.delete({
      where: { id },
    });

    return {
      data: true,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to delete customer:', error);
    return {
      data: null,
      error: 'Failed to delete customer',
      success: false
    };
  }
}

/**
 * Search customers semantically
 */
export async function searchCustomers(query: string, userId: string, limit: number = 5): Promise<ServiceResponse<Customer[]>> {
  try {
    // Search customers in Supabase
    const searchResults = await searchSimilar(COLLECTIONS.CUSTOMERS, query, limit);

    // Extract customer IDs from search results
    const customerIds = searchResults.map(result => String(result.id));

    // Fetch full customer data from database
    const customers = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        userId,
      },
      include: {
        devices: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Sort customers in the same order as search results
    const sortedCustomers = customerIds
      .map(id => customers.find(customer => customer.id === id))
      .filter(Boolean) as Customer[];

    return {
      data: sortedCustomers,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to search customers:', error);
    return {
      data: null,
      error: 'Failed to search customers',
      success: false
    };
  }
}

/**
 * Get customer activity (service orders, communications, etc.)
 */
export async function getCustomerActivity(
  customerId: string,
  userId: string,
  limit: number = 10
): Promise<ServiceResponse<any[]>> {
  try {
    // Verify customer exists and belongs to user
    const customer = await prisma.customer.findUnique({
      where: { id: customerId, userId },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Get service orders for this customer
    const serviceOrders = await prisma.serviceOrder.findMany({
      where: { customerId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        title: true,
        status: true,
        createdAt: true,
      },
    });

    // Get communications for this customer
    const communications = await prisma.communication.findMany({
      where: { customerId },
      orderBy: { timestamp: 'desc' },
      take: limit,
      select: {
        id: true,
        channel: true,
        subject: true,
        content: true,
        timestamp: true,
      },
    });

    // Get device events for this customer
    const deviceEvents = await prisma.deviceEvent.findMany({
      where: {
        device: {
          customerId,
        },
      },
      orderBy: { timestamp: 'desc' },
      take: limit,
      select: {
        id: true,
        type: true,
        description: true,
        timestamp: true,
        device: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Combine all activities and format them
    const activities = [
      ...serviceOrders.map(order => ({
        id: `so-${order.id}`,
        type: 'SERVICE_ORDER',
        title: `Service Order: ${order.title}`,
        description: `Status: ${order.status}`,
        timestamp: order.createdAt,
        entityId: order.id,
        entityType: 'SERVICE_ORDER',
      })),
      ...communications.map(comm => ({
        id: `comm-${comm.id}`,
        type: 'COMMUNICATION',
        title: `${comm.channel}: ${comm.subject || 'No subject'}`,
        description: comm.content.length > 100 ? `${comm.content.substring(0, 100)}...` : comm.content,
        timestamp: comm.timestamp,
        entityId: comm.id,
        entityType: 'COMMUNICATION',
      })),
      ...deviceEvents.map(event => ({
        id: `dev-${event.id}`,
        type: 'DEVICE',
        title: `Device Event: ${event.device.name}`,
        description: event.description,
        timestamp: event.timestamp,
        entityId: event.id,
        entityType: 'DEVICE_EVENT',
        deviceId: event.device.id,
      })),
    ];

    // Sort all activities by timestamp (newest first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Limit to the requested number
    const limitedActivities = activities.slice(0, limit);

    return {
      data: limitedActivities,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch customer activity:', error);
    return {
      data: null,
      error: 'Failed to fetch customer activity',
      success: false
    };
  }
}
