import { prisma } from "~/db.server";
import type { ServiceOrder as PrismaServiceOrder } from "@prisma/client";
import * as qdrantService from "./qdrant.server";

export interface ServiceOrder extends PrismaServiceOrder {}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  priority?: string;
  type?: string;
  customerId?: string;
  deviceId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all service orders with pagination, filtering, and sorting
 */
export async function getServiceOrders(
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<PaginatedResponse<ServiceOrder>> {
  try {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = pagination;

    const { search, status, priority, type, customerId, deviceId, dateFrom, dateTo } = filters;

    // Build where clause
    const where: any = { userId };

    // Add search filter if provided
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add status filter if provided
    if (status) {
      where.status = status;
    }

    // Add priority filter if provided
    if (priority) {
      where.priority = priority;
    }

    // Add type filter if provided
    if (type) {
      where.type = type;
    }

    // Add customer filter if provided
    if (customerId) {
      where.customerId = customerId;
    }

    // Add device filter if provided
    if (deviceId) {
      where.deviceId = deviceId;
    }

    // Add date range filters if provided
    if (dateFrom) {
      where.scheduledDate = {
        ...where.scheduledDate,
        gte: new Date(dateFrom),
      };
    }

    if (dateTo) {
      where.scheduledDate = {
        ...where.scheduledDate,
        lte: new Date(dateTo),
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.serviceOrder.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get service orders with pagination
    const serviceOrders = await prisma.serviceOrder.findMany({
      where,
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
        device: {
          select: {
            id: true,
            name: true,
            model: true,
          },
        },
      },
    });

    return {
      data: serviceOrders,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch service orders:', error);
    return {
      data: null,
      error: 'Failed to fetch service orders',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}

/**
 * Get a service order by ID
 */
export async function getServiceOrderById(id: string, userId: string): Promise<ServiceResponse<ServiceOrder>> {
  try {
    const serviceOrder = await prisma.serviceOrder.findUnique({
      where: { id, userId },
      include: {
        customer: true,
        device: true,
      },
    });

    if (!serviceOrder) {
      return {
        data: null,
        error: 'Service order not found',
        success: false
      };
    }

    return {
      data: serviceOrder,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch service order:', error);
    return {
      data: null,
      error: 'Failed to fetch service order',
      success: false
    };
  }
}

/**
 * Create a new service order
 */
export async function createServiceOrder(
  data: Omit<ServiceOrder, 'id' | 'createdAt' | 'updatedAt'>,
  userId: string
): Promise<ServiceResponse<ServiceOrder>> {
  try {
    // Verify customer exists and belongs to user
    const customer = await prisma.customer.findUnique({
      where: { id: data.customerId, userId },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // If deviceId is provided, verify it exists and belongs to user and customer
    if (data.deviceId) {
      const device = await prisma.device.findUnique({
        where: {
          id: data.deviceId,
          userId,
          customerId: data.customerId,
        },
      });

      if (!device) {
        return {
          data: null,
          error: 'Device not found, access denied, or does not belong to the specified customer',
          success: false
        };
      }
    }

    // Add userId to data and ensure consistent field naming
    const serviceOrderData = {
      ...data,
      userId,
    };

    // Handle the case where completionDate is passed instead of completedDate
    const anyServiceOrderData = serviceOrderData as any;
    if (anyServiceOrderData.completionDate !== undefined) {
      anyServiceOrderData.completedDate = anyServiceOrderData.completionDate;
      delete anyServiceOrderData.completionDate;
    }

    // Create service order in database
    const serviceOrder = await prisma.serviceOrder.create({
      data: anyServiceOrderData, // Use anyServiceOrderData to include the fixed field names
    });

    // Index service order in Qdrant for semantic search
    try {
      await qdrantService.indexServiceOrder(prisma, serviceOrder.id);
    } catch (indexError) {
      console.error('Failed to index service order in Qdrant:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: serviceOrder,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create service order:', error);
    return {
      data: null,
      error: 'Failed to create service order',
      success: false
    };
  }
}

/**
 * Update an existing service order
 */
export async function updateServiceOrder(
  id: string,
  data: Partial<Omit<ServiceOrder, 'id' | 'createdAt' | 'updatedAt'>>,
  userId: string
): Promise<ServiceResponse<ServiceOrder>> {
  try {
    // Check if service order exists and belongs to user
    const existingServiceOrder = await prisma.serviceOrder.findUnique({
      where: { id, userId },
    });

    if (!existingServiceOrder) {
      return {
        data: null,
        error: 'Service order not found or access denied',
        success: false
      };
    }

    // If customerId is being updated, verify the new customer exists and belongs to user
    if (data.customerId && data.customerId !== existingServiceOrder.customerId) {
      const customer = await prisma.customer.findUnique({
        where: { id: data.customerId, userId },
      });

      if (!customer) {
        return {
          data: null,
          error: 'Customer not found or access denied',
          success: false
        };
      }
    }

    // If deviceId is being updated, verify the new device exists and belongs to user and customer
    if (data.deviceId && data.deviceId !== existingServiceOrder.deviceId) {
      const customerId = data.customerId || existingServiceOrder.customerId;

      const device = await prisma.device.findUnique({
        where: {
          id: data.deviceId,
          userId,
          customerId,
        },
      });

      if (!device) {
        return {
          data: null,
          error: 'Device not found, access denied, or does not belong to the specified customer',
          success: false
        };
      }
    }

    // Ensure consistent field naming (completedDate vs completionDate)
    const dataToUpdate = { ...data };

    // Handle the case where completionDate is passed instead of completedDate
    // Using type assertion to avoid TypeScript errors
    const anyData = dataToUpdate as any;
    if (anyData.completionDate !== undefined) {
      anyData.completedDate = anyData.completionDate;
      delete anyData.completionDate;
    }

    // Update service order
    const updatedServiceOrder = await prisma.serviceOrder.update({
      where: { id },
      data: anyData, // Use anyData instead of dataToUpdate to include the fixed field names
    });

    // Re-index service order in Qdrant
    try {
      await qdrantService.indexServiceOrder(prisma, id);
    } catch (indexError) {
      console.error('Failed to re-index service order in Qdrant:', indexError);
      // Don't fail the operation if indexing fails
    }

    return {
      data: updatedServiceOrder,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to update service order:', error);
    return {
      data: null,
      error: 'Failed to update service order',
      success: false
    };
  }
}

/**
 * Delete a service order
 */
export async function deleteServiceOrder(id: string, userId: string): Promise<ServiceResponse<boolean>> {
  try {
    // Check if service order exists and belongs to user
    const existingServiceOrder = await prisma.serviceOrder.findUnique({
      where: { id, userId },
    });

    if (!existingServiceOrder) {
      return {
        data: null,
        error: 'Service order not found or access denied',
        success: false
      };
    }

    // Delete service order from Qdrant first
    try {
      await qdrantService.deleteEmbedding(qdrantService.COLLECTIONS.SERVICE_ORDERS, id);
    } catch (deleteError) {
      console.error('Failed to delete service order from Qdrant:', deleteError);
      // Don't fail the operation if Qdrant deletion fails
    }

    // Delete service order from database
    await prisma.serviceOrder.delete({
      where: { id },
    });

    return {
      data: true,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to delete service order:', error);
    return {
      data: null,
      error: 'Failed to delete service order',
      success: false
    };
  }
}

/**
 * Search service orders semantically
 */
export async function searchServiceOrders(query: string, userId: string, limit: number = 5): Promise<ServiceResponse<ServiceOrder[]>> {
  try {
    // Search service orders in Qdrant
    const searchResults = await qdrantService.searchSimilar(qdrantService.COLLECTIONS.SERVICE_ORDERS, query, limit);

    // Extract service order IDs from search results
    const serviceOrderIds = searchResults.map(result => String(result.id));

    // Fetch full service order data from database
    const serviceOrders = await prisma.serviceOrder.findMany({
      where: {
        id: { in: serviceOrderIds },
        userId,
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
        device: {
          select: {
            id: true,
            name: true,
            model: true,
          },
        },
      },
    });

    // Sort service orders in the same order as search results
    const sortedServiceOrders = serviceOrderIds
      .map(id => serviceOrders.find((order: any) => order.id === id))
      .filter(Boolean) as ServiceOrder[];

    return {
      data: sortedServiceOrders,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to search service orders:', error);
    return {
      data: null,
      error: 'Failed to search service orders',
      success: false
    };
  }
}