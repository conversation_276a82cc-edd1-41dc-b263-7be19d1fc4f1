generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String                      @id @default(cuid())
  email                      String                      @unique
  name                       String?
  phone                      String?
  role                       String                      @default("USER")
  isActive                   Boolean                     @default(true)
  lastLogin                  DateTime?
  customerId                 String?
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @updatedAt
  mfaEnabled                 Boolean                     @default(false)
  mfaSecret                  String?
  mfaRecoveryCodes           String?                     // JSON string of recovery codes
  mfaVerified                Boolean                     @default(false)
  aiConversations            AIConversation[]
  calendar                   CalendarEntry[]
  customers                  Customer[]
  customerPortalAccess       CustomerPortalAccess?
  devices                    Device[]
  installationProjects       InstallationProject[]
  memoryEntities             MemoryEntity[]
  memoryItems                MemoryItem[]
  memoryTags                 MemoryTag[]
  notes                      Note[]
  notifications              Notification[]
  offers                     Offer[]
  outlookCalendarIntegration OutlookCalendarIntegration?
  password                   Password?
  assignedMilestones         ProjectMilestone[]         @relation("AssignedMilestones")
  createdMilestones         ProjectMilestone[]         @relation("CreatedMilestones")
  serviceOrders              ServiceOrder[]
  settings                   UserSettings?
  workflows                  Workflow[]
}

model Password {
  hash   String
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Note {
  id        String   @id @default(cuid())
  title     String
  body      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Customer {
  id                     String              @id @default(cuid())
  name                   String
  email                  String?
  phone                  String?
  address                String?
  city                   String?
  postalCode             String?
  country                String?
  notes                  String?
  hasPortalAccess        Boolean             @default(false)
  portalAccessInviteSent DateTime?
  portalAccessLastLogin  DateTime?
  stripeCustomerId       String?             // Customer ID from Stripe
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt
  userId                 String
  user                   User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  devices                Device[]
  invoices               Invoice[]
  offers                 Offer[]
  serviceOrders          ServiceOrder[]
  payments               Payment[]
  paymentMethods         PaymentMethod[]
  installationProjects   InstallationProject[]
}

model Device {
  id                     String                  @id @default(cuid())
  name                   String
  model                  String?
  serialNumber           String?
  manufacturer           String?
  installationDate       DateTime?
  warrantyExpiryDate     DateTime?
  notes                  String?
  type                   String?
  capacity               String?
  efficiency             String?
  refrigerantType        String?
  lastMaintenanceDate    DateTime?
  nextMaintenanceDate    DateTime?
  maintenanceInterval    Int?
  customerVisible        Boolean                 @default(true)
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  customerId             String
  userId                 String
  user                   User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  customer               Customer                @relation(fields: [customerId], references: [id], onDelete: Cascade)
  telemetry              DeviceTelemetry[]
  maintenancePredictions MaintenancePrediction[]
  serviceOrders          ServiceOrder[]
}

model ServiceOrder {
  id                   String              @id @default(cuid())
  title                String
  description          String?
  status               String              @default("PENDING")
  installationProjectId String?
  installationProject  InstallationProject? @relation(fields: [installationProjectId], references: [id])
  priority             String          @default("MEDIUM")
  type                 String          @default("SERVICE")
  scheduledDate        DateTime?
  completedDate        DateTime?
  notes                String?
  assignedTechnicianId String?
  technicianNotes      String?
  customerVisible      Boolean         @default(true)
  customerFeedback     String?
  customerRating       Int?
  offlineId            String?
  lastSyncedAt         DateTime?
  createdAt            DateTime        @default(now())
  updatedAt            DateTime        @updatedAt
  customerId           String
  deviceId             String?
  userId               String
  offerId              String?
  calendarEntries      CalendarEntry[]
  invoices             Invoice[]
  user                 User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  device               Device?         @relation(fields: [deviceId], references: [id])
  customer             Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade)
  offer                Offer?          @relation(fields: [offerId], references: [id])
  serviceReports       ServiceReport[]
}

model CalendarEntry {
  id                String        @id @default(cuid())
  title             String
  description       String?
  startTime         DateTime
  endTime           DateTime
  location          String?
  isAllDay          Boolean       @default(false)
  color             String?
  outlookEventId    String?
  outlookLastSync   DateTime?
  serviceOrderId    String?

  // Semantic analysis fields
  type              String?
  customer          String?
  technician        String?
  device            String?
  deviceCount       Int?
  priority          String?
  status            String?
  clientContact     String?
  technicalIssues   String?
  spareParts        String?
  costAmount        Float?
  costCurrency      String?
  costDescription   String?
  keywords          String?
  semanticAnalysis  String?
  analysisTimestamp DateTime?

  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  userId            String
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  serviceOrder      ServiceOrder? @relation(fields: [serviceOrderId], references: [id])
}

model VectorEmbedding {
  id         String   @id @default(cuid())
  objectType String
  objectId   String
  content    String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model CustomerPortalAccess {
  id                    String   @id @default(cuid())
  userId                String   @unique
  canViewDevices        Boolean  @default(true)
  canViewServiceHistory Boolean  @default(true)
  canRequestService     Boolean  @default(true)
  canViewInvoices       Boolean  @default(true)
  emailNotifications    Boolean  @default(true)
  smsNotifications      Boolean  @default(false)
  pushNotifications     Boolean  @default(false)
  phoneNumber           String?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Invoice {
  id                   String        @id @default(cuid())
  invoiceNumber        String?
  issueDate            DateTime?
  dueDate              DateTime?
  totalAmount          Float?
  taxAmount            Float?
  status               String        @default("PENDING")
  notes                String?
  originalDocumentUrl  String?
  processedDocumentUrl String?
  ocrProcessingStatus  String        @default("PENDING")
  ocrConfidenceScore   Float?
  ocrProcessedAt       DateTime?
  ocrErrorMessage      String?
  customerId           String
  serviceOrderId       String?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  buyerInfo            String?
  sellerInfo           String?
  paymentStatus        String        @default("UNPAID") // UNPAID, PARTIALLY_PAID, PAID, REFUNDED
  paymentMethod        String?       // CREDIT_CARD, BANK_TRANSFER, CASH, OTHER
  paymentDueDate       DateTime?
  serviceOrder         ServiceOrder? @relation(fields: [serviceOrderId], references: [id])
  customer             Customer      @relation(fields: [customerId], references: [id], onDelete: Cascade)
  items                InvoiceItem[]
  payments             Payment[]
}

model InvoiceItem {
  id          String   @id @default(cuid())
  description String
  quantity    Float
  unitPrice   Float
  totalPrice  Float
  taxRate     Float?
  invoiceId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
}

model ServiceReport {
  id                     String             @id @default(cuid())
  title                  String
  description            String?
  workPerformed          String?
  partsUsed              String?            // Pozostawione dla kompatybilności wstecznej
  recommendations        String?
  technicianSignatureUrl String?
  customerSignatureUrl   String?
  signedAt               DateTime?
  photoUrls              String?
  ocrProcessingStatus    String             @default("PENDING")
  ocrProcessedAt         DateTime?
  ocrErrorMessage        String? // Add this line
  serviceOrderId         String
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  serviceOrder           ServiceOrder      @relation(fields: [serviceOrderId], references: [id], onDelete: Cascade)
  usedParts              ServiceReportPart[]
}

model OutlookCalendarIntegration {
  id            String    @id @default(cuid())
  accessToken   String
  refreshToken  String
  expiresAt     DateTime
  autoSync      Boolean   @default(true)
  syncFrequency String    @default("HOURLY")
  lastSyncTime  DateTime?
  userId        String    @unique
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model MemoryItem {
  id         String         @id @default(cuid())
  title      String
  content    String
  source     String?
  type       String
  importance Int            @default(1)
  vectorId   String?
  userId     String
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  user       User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  entities   MemoryEntity[] @relation("MemoryItemToEntity")
  tags       MemoryTag[]    @relation("MemoryItemToTag")
}

model MemoryTag {
  id          String       @id @default(cuid())
  name        String
  color       String?
  userId      String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  memoryItems MemoryItem[] @relation("MemoryItemToTag")

  @@unique([name, userId])
}

model MemoryEntity {
  id          String                 @id @default(cuid())
  name        String
  type        String
  description String?
  userId      String
  createdAt   DateTime               @default(now())
  updatedAt   DateTime               @updatedAt
  user        User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  relatedTo   MemoryEntityRelation[] @relation("EntityRelationTo")
  relations   MemoryEntityRelation[] @relation("EntityRelationFrom")
  memoryItems MemoryItem[]           @relation("MemoryItemToEntity")
}

model MemoryEntityRelation {
  id           String       @id @default(cuid())
  type         String
  fromEntityId String
  toEntityId   String
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  toEntity     MemoryEntity @relation("EntityRelationTo", fields: [toEntityId], references: [id], onDelete: Cascade)
  fromEntity   MemoryEntity @relation("EntityRelationFrom", fields: [fromEntityId], references: [id], onDelete: Cascade)
}

model DeviceTelemetry {
  id          String   @id @default(cuid())
  timestamp   DateTime
  temperature Float?
  humidity    Float?
  pressure    Float?
  vibration   Float?
  noise       Float?
  powerUsage  Float?
  runtime     Float?
  cycles      Int?
  errorCodes  String?
  customData  String?
  deviceId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  device      Device   @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model MaintenancePrediction {
  id                   String    @id @default(cuid())
  predictionDate       DateTime
  failureProbability   Float
  predictedFailureDate DateTime?
  predictedComponent   String?
  recommendedAction    String?
  confidence           Float
  modelVersion         String?
  modelFeatures        String?
  deviceId             String
  maintenancePerformed Boolean   @default(false)
  maintenanceDate      DateTime?
  maintenanceNotes     String?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  device               Device    @relation(fields: [deviceId], references: [id], onDelete: Cascade)
}

model Workflow {
  id             String              @id @default(cuid())
  name           String
  description    String?
  isActive       Boolean             @default(true)
  triggerType    String
  triggerConfig  String
  conditions     String?
  actions        String
  lastExecuted   DateTime?
  executionCount Int                 @default(0)
  userId         String
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  user           User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  executions     WorkflowExecution[]
}

model WorkflowExecution {
  id            String    @id @default(cuid())
  startTime     DateTime
  endTime       DateTime?
  status        String
  result        String?
  error         String?
  workflowId    String
  triggerData   String?
  actionResults String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  workflow      Workflow  @relation(fields: [workflowId], references: [id], onDelete: Cascade)
}

model Event {
  id              String   @id @default(cuid())
  type            String
  data            String   // JSON string of event data
  metadata        String   // JSON string of event metadata
  processed       Boolean  @default(false)
  processingError String?
  processedAt     DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model AIConversation {
  id                String      @id @default(cuid())
  title             String?
  summary           String?
  userId            String
  contextReferences String?
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages          AIMessage[]
}

model AIMessage {
  id               String         @id @default(cuid())
  role             String
  content          String
  conversationId   String
  tokens           Int?
  processingTime   Int?
  contextUsed      String?
  memoryReferences String?
  createdAt        DateTime       @default(now())
  conversation     AIConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
}

model Notification {
  id           String   @id @default(cuid())
  type         String
  title        String
  message      String
  priority     String   @default("medium")
  status       String   @default("unread")
  link         String?
  channels     String   @default("[]")
  emailSent    Boolean?
  smsSent      Boolean?
  pushSent     Boolean?
  phoneNumber  String?
  emailAddress String?
  emailSubject String?
  emailHtml    String?
  userId       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserSettings {
  id                       String   @id @default(cuid())
  theme                    String   @default("system")
  fontSize                 String   @default("medium")
  colorScheme              String   @default("default")
  dashboardLayout          String?
  showStats                Boolean  @default(true)
  showRecentOrders         Boolean  @default(true)
  showUpcomingEvents       Boolean  @default(true)
  showQuickActions         Boolean  @default(true)
  statsToShow              String?
  emailNotifications       Boolean  @default(true)
  inAppNotifications       Boolean  @default(true)
  pushNotifications        Boolean  @default(false)
  smsNotifications         Boolean  @default(false)
  serviceOrderUpdates      Boolean  @default(true)
  calendarReminders        Boolean  @default(true)
  systemUpdates            Boolean  @default(true)
  deviceAlerts             Boolean  @default(true)
  newMessages              Boolean  @default(true)
  serviceOrderChannels     String?
  calendarReminderChannels String?
  systemUpdateChannels     String?
  deviceAlertChannels      String?
  newMessageChannels       String?
  notificationEmail        String?
  notificationPhone        String?
  userId                   String   @unique
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  user                     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model CompanySettings {
  id                String   @id @default(cuid())
  name              String
  address           String?
  city              String?
  postalCode        String?
  country           String?
  phone             String?
  email             String?
  website           String?
  taxId             String?
  logoUrl           String?
  primaryColor      String?
  secondaryColor    String?
  bankName          String?
  bankAccountNumber String?
  bankSwift         String?
  invoiceFooter     String?
  reportFooter      String?
  termsAndConditions String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model Offer {
  id                String      @id @default(cuid())
  title             String
  description       String?
  status            String      @default("DRAFT") // DRAFT, SENT, ACCEPTED, REJECTED, EXPIRED
  validUntil        DateTime?
  totalAmount       Float
  taxAmount         Float?
  discountAmount    Float?
  notes             String?
  sentAt            DateTime?
  viewedAt          DateTime?
  respondedAt       DateTime?
  followUpDate      DateTime?
  followUpNotes     String?
  templateId        String?
  customerId        String
  userId            String
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  customer          Customer    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  template          OfferTemplate? @relation(fields: [templateId], references: [id])
  items             OfferItem[]
  variants          OfferVariant[]
  serviceOrders     ServiceOrder[]
}

model OfferItem {
  id          String   @id @default(cuid())
  description String
  quantity    Float
  unitPrice   Float
  totalPrice  Float
  taxRate     Float?
  offerId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  offer       Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
}

model OfferTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  content     String   // JSON string with template structure
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  offers      Offer[]
}

model OfferVariant {
  id          String   @id @default(cuid())
  name        String
  description String?
  totalAmount Float
  taxAmount   Float?
  isSelected  Boolean  @default(false)
  offerId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  offer       Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
  items       OfferVariantItem[]
}

model OfferVariantItem {
  id            String      @id @default(cuid())
  description   String
  quantity      Float
  unitPrice     Float
  totalPrice    Float
  taxRate       Float?
  variantId     String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  variant       OfferVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)
}

model Payment {
  id                 String    @id @default(cuid())
  amount             Float
  currency           String    @default("PLN")
  status             String    @default("PENDING") // PENDING, COMPLETED, FAILED, REFUNDED
  paymentMethod      String    // CREDIT_CARD, BANK_TRANSFER, CASH, OTHER
  paymentMethodId    String?   // ID from payment provider
  paymentIntentId    String?   // ID from payment provider
  transactionId      String?   // ID from payment provider
  receiptUrl         String?
  refundId           String?
  refundAmount       Float?
  refundReason       String?
  refundedAt         DateTime?
  metadata           String?   // JSON string with additional data
  invoiceId          String
  customerId         String
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @default(now()) @updatedAt
  invoice            Invoice   @relation(fields: [invoiceId], references: [id])
  customer           Customer  @relation(fields: [customerId], references: [id])
}

model PaymentMethod {
  id                 String    @id @default(cuid())
  type               String    // CREDIT_CARD, BANK_ACCOUNT
  name               String    // Friendly name (e.g. "My Visa Card")
  isDefault          Boolean   @default(false)
  lastFourDigits     String?
  expiryMonth        Int?
  expiryYear         Int?
  brand              String?   // Visa, Mastercard, etc.
  paymentMethodId    String?   // ID from payment provider
  customerId         String
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @default(now()) @updatedAt
  customer           Customer  @relation(fields: [customerId], references: [id])
}

// Models for Inventory Management System

model InventoryPart {
  id                  String                @id @default(cuid())
  name                String
  partNumber          String?               // Manufacturer part number
  serialNumber        String?               // For unique parts with serial numbers
  description         String?
  category            String?               // Category of part (e.g., Filters, Refrigerants)
  type                String?               // Type of part (e.g., A/C filter, Compressor)
  manufacturer        String?
  model               String?               // Equipment model compatibility
  sku                 String?               // Internal stock keeping unit
  barcode             String?               // Barcode/QR code
  currentStock        Int                   @default(0)
  minimumStock        Int                   @default(1)
  reorderPoint        Int                   @default(5)
  costPrice           Float?
  sellingPrice        Float?
  taxRate             Float                 @default(0)
  location            String?               // Default location in warehouse
  unitOfMeasure       String                @default("EA") // EA, KG, LTR, etc.
  isActive            Boolean               @default(true)
  notes               String?
  imageUrl            String?
  tags                String?               // JSON array of tags
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  supplierId          String?
  supplier            Supplier?             @relation(fields: [supplierId], references: [id])
  inventoryLocations  PartLocation[]
  transactions        InventoryTransaction[]
  serviceReportParts  ServiceReportPart[]
  reservations        PartReservation[]
}

model ServiceReportPart {
  id              String        @id @default(cuid())
  quantity        Float
  unitPrice       Float?
  totalPrice      Float?
  notes           String?
  serviceReportId String
  partId          String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  serviceReport   ServiceReport @relation(fields: [serviceReportId], references: [id], onDelete: Cascade)
  part            InventoryPart @relation(fields: [partId], references: [id])
}

model InventoryLocation {
  id          String          @id @default(cuid())
  name        String
  description String?
  type        String?         // Warehouse, Van, Storage Rack, etc.
  address     String?
  notes       String?
  capacity    Int?            // Maximum capacity
  isActive    Boolean         @default(true)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  parts       PartLocation[]
}

model PartLocation {
  id           String            @id @default(cuid())
  quantity     Int              @default(0)
  aisle        String?
  rack         String?
  shelf        String?
  bin          String?
  notes        String?
  partId       String
  locationId   String
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  part         InventoryPart    @relation(fields: [partId], references: [id], onDelete: Cascade)
  location     InventoryLocation @relation(fields: [locationId], references: [id], onDelete: Cascade)
}

model InventoryTransaction {
  id                   String        @id @default(cuid())
  type                 String        // INBOUND, OUTBOUND, TRANSFER, ADJUSTMENT
  quantity             Float
  unitPrice            Float?
  totalPrice           Float?
  reference            String?       // Invoice number, service order ID, etc.
  referenceType        String?       // PURCHASE, SERVICE, TRANSFER, ADJUSTMENT
  notes                String?
  partId               String
  sourceLocationId     String?
  destinationLocationId String?
  serviceReportId      String?
  supplierId           String?
  performedById        String?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  part                 InventoryPart @relation(fields: [partId], references: [id])
}

model Supplier {
  id                String          @id @default(cuid())
  name              String
  contactPerson     String?
  email             String?
  phone             String?
  address           String?
  city              String?
  postalCode        String?
  country           String?
  website           String?
  taxId             String?
  paymentTerms      String?
  notes             String?
  isActive          Boolean         @default(true)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  parts             InventoryPart[]
}

model PartReservation {
  id                String        @id @default(cuid())
  quantity          Int
  reservationDate   DateTime
  expiryDate        DateTime?
  status            String        @default("ACTIVE") // ACTIVE, FULFILLED, CANCELLED
  notes             String?
  partId            String
  serviceOrderId    String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  part              InventoryPart @relation(fields: [partId], references: [id])
}

model InstallationProject {
  id                String           @id @default(cuid())
  name              String
  description       String?
  status            String           @default("PLANNING") // PLANNING, IN_PROGRESS, ON_HOLD, COMPLETED, CANCELLED
  startDate         DateTime?
  targetDate        DateTime?
  completionDate    DateTime?
  address           String?
  city              String?
  state             String?
  postalCode        String?
  country           String?
  budget            Float?
  actualCost        Float?
  notes             String?
  isActive          Boolean          @default(true)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  customerId        String?
  customer          Customer?        @relation(fields: [customerId], references: [id])
  milestones        ProjectMilestone[]
  serviceOrders     ServiceOrder[]
}

model ProjectMilestone {
  id                  String             @id @default(cuid())
  title               String
  description         String?
  dueDate             DateTime?
  completionDate      DateTime?
  status              String             @default("PENDING") // PENDING, IN_PROGRESS, COMPLETED, DELAYED, CANCELLED
  notes               String?
  isCritical          Boolean            @default(false)
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  projectId           String
  project             InstallationProject @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignedToId        String?
  assignedTo          User?              @relation("AssignedMilestones", fields: [assignedToId], references: [id])
  createdById         String
  createdBy           User               @relation("CreatedMilestones", fields: [createdById], references: [id], onDelete: Cascade)
}

model CustomField {
  id          String              @id @default(cuid())
  name        String
  label       String
  fieldType   String              // TEXT, NUMBER, DATE, BOOLEAN, SELECT, TEXTAREA
  entityType  String              // CUSTOMER, SERVICE_ORDER, DEVICE, etc.
  isRequired  Boolean             @default(false)
  isActive    Boolean             @default(true)
  options     String?             // JSON string for SELECT type fields
  defaultValue String?
  validation  String?             // JSON string for validation rules
  order       Int                 @default(0)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  values      CustomFieldValue[]

  @@unique([name, entityType])
}

model CustomFieldValue {
  id        String      @id @default(cuid())
  entityId  String
  fieldId   String
  value     String?
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  field     CustomField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  @@unique([entityId, fieldId])
}

model AdminSettings {
  id                    String   @id @default(cuid())
  key                   String   @unique
  value                 String?
  description           String?
  category              String?
  isPublic              Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}
