#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get TypeScript errors and extract unused import information
const { execSync } = require('child_process');

try {
  const tscOutput = execSync('npx tsc --noEmit', { 
    cwd: process.cwd(),
    encoding: 'utf8',
    stdio: 'pipe'
  });
} catch (error) {
  const output = error.stdout || error.stderr || '';
  
  // Parse unused import errors (TS6133, TS6196, TS6192)
  const lines = output.split('\n');
  const unusedImports = [];
  
  lines.forEach(line => {
    if (line.includes('error TS6133') || line.includes('error TS6196') || line.includes('error TS6192')) {
      const match = line.match(/^(.+?)\((\d+),(\d+)\): error TS\d+: (.+)$/);
      if (match) {
        const [, filePath, lineNum, colNum, message] = match;
        unusedImports.push({
          file: filePath,
          line: parseInt(lineNum),
          column: parseInt(colNum),
          message: message
        });
      }
    }
  });
  
  console.log(`Found ${unusedImports.length} unused import/variable errors`);
  
  // Group by file
  const fileGroups = {};
  unusedImports.forEach(item => {
    if (!fileGroups[item.file]) {
      fileGroups[item.file] = [];
    }
    fileGroups[item.file].push(item);
  });
  
  // Process each file
  Object.keys(fileGroups).forEach(filePath => {
    console.log(`Processing ${filePath}...`);
    const errors = fileGroups[filePath];
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      // Sort errors by line number (descending) to avoid line number shifts
      errors.sort((a, b) => b.line - a.line);
      
      errors.forEach(error => {
        const lineIndex = error.line - 1;
        if (lineIndex >= 0 && lineIndex < lines.length) {
          const line = lines[lineIndex];
          
          // Handle different types of unused imports
          if (error.message.includes('All imports in import declaration are unused')) {
            // Remove entire import line
            lines.splice(lineIndex, 1);
          } else if (error.message.includes('is declared but never used')) {
            // Extract the unused identifier
            const match = error.message.match(/'([^']+)' is declared but never used/);
            if (match) {
              const unusedName = match[1];
              
              // Remove from import statement
              if (line.includes('import')) {
                let newLine = line;
                
                // Handle different import patterns
                if (line.includes(`{ ${unusedName} }`)) {
                  newLine = line.replace(`{ ${unusedName} }`, '{}');
                } else if (line.includes(`{${unusedName}}`)) {
                  newLine = line.replace(`{${unusedName}}`, '{}');
                } else if (line.includes(`, ${unusedName}`)) {
                  newLine = line.replace(`, ${unusedName}`, '');
                } else if (line.includes(`${unusedName}, `)) {
                  newLine = line.replace(`${unusedName}, `, '');
                } else if (line.includes(`{ ${unusedName},`)) {
                  newLine = line.replace(`${unusedName},`, '');
                } else if (line.includes(`, ${unusedName} }`)) {
                  newLine = line.replace(`, ${unusedName}`, '');
                }
                
                // If import becomes empty, remove the line
                if (newLine.match(/import\s*{\s*}\s*from/) || newLine.match(/import\s*{}\s*from/)) {
                  lines.splice(lineIndex, 1);
                } else {
                  lines[lineIndex] = newLine;
                }
              }
            }
          }
        }
      });
      
      // Write back the modified content
      const newContent = lines.join('\n');
      if (newContent !== content) {
        fs.writeFileSync(filePath, newContent);
        console.log(`  Fixed ${errors.length} issues in ${filePath}`);
      }
      
    } catch (err) {
      console.error(`Error processing ${filePath}:`, err.message);
    }
  });
}