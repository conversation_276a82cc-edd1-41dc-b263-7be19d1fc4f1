# Agent Protocol Server Dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Copy Poetry configuration files
COPY pyproject.toml poetry.lock ./

# Configure Poetry
RUN poetry config virtualenvs.create false

# Install Python dependencies
RUN poetry install --only=main --no-root

# Copy application code
COPY ./ap_server ./ap_server
COPY ./init.sql ./init.sql

# Create directories for data and cache
RUN mkdir -p /app/data /app/logs

# Set environment variables
ENV PYTHONPATH=/app
ENV MODEL_NAME=google/gemma-3-4b-it
ENV HF_HUB_ENABLE_HF_TRANSFER=1
ENV HF_HOME=/app/huggingface_cache

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Start the server
CMD ["uvicorn", "ap_server.main:app", "--host", "0.0.0.0", "--port", "8001"]